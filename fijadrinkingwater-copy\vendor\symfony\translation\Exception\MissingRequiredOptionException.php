<?php

/*
 * This file is part of the Symfony package.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

namespace Symfony\Component\Translation\Exception;

/**
 * <AUTHOR> <<EMAIL>>
 */
class MissingRequiredOptionException extends IncompleteDsnException
{
    public function __construct(string $option, string $dsn = null, \Throwable $previous = null)
    {
        $message = sprintf('The option "%s" is required but missing.', $option);

        parent::__construct($message, $dsn, $previous);
    }
}
