import{v as c,j as e,$ as m}from"./app-BfLjgcCU.js";function p({status:o,canResetPassword:x}){const{data:n,setData:t,post:d,processing:a,errors:r,reset:i}=c({phone:"",password:"",remember:!1}),l=s=>{s.preventDefault(),d(route("login"),{onFinish:()=>i("password")})};return e.jsxs(e.Fragment,{children:[e.jsx(m,{title:"Log in"}),e.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"max-w-md w-full space-y-8",children:[e.jsxs("div",{children:[e.jsx("img",{className:"mx-auto h-12 w-auto",src:"/logo.png",alt:"FIJA Water"}),e.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Sign in to your account"})]}),e.jsxs("form",{className:"mt-8 space-y-6",onSubmit:l,children:[e.jsxs("div",{className:"rounded-md shadow-sm -space-y-px",children:[e.jsxs("div",{children:[e.jsx("label",{htmlFor:"phone",className:"sr-only",children:"Phone number"}),e.jsx("input",{id:"phone",name:"phone",type:"tel",autoComplete:"tel",required:!0,className:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm",placeholder:"Phone number",value:n.phone,onChange:s=>t("phone",s.target.value)}),r.phone&&e.jsx("div",{className:"text-red-600 text-sm mt-1",children:r.phone})]}),e.jsxs("div",{children:[e.jsx("label",{htmlFor:"password",className:"sr-only",children:"Password"}),e.jsx("input",{id:"password",name:"password",type:"password",autoComplete:"current-password",required:!0,className:"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm",placeholder:"Password",value:n.password,onChange:s=>t("password",s.target.value)}),r.password&&e.jsx("div",{className:"text-red-600 text-sm mt-1",children:r.password})]})]}),e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("input",{id:"remember-me",name:"remember",type:"checkbox",className:"h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded",checked:n.remember,onChange:s=>t("remember",s.target.checked)}),e.jsx("label",{htmlFor:"remember-me",className:"ml-2 block text-sm text-gray-900",children:"Remember me"})]})}),e.jsx("div",{children:e.jsx("button",{type:"submit",disabled:a,className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50",children:a?"Signing in...":"Sign in"})}),o&&e.jsx("div",{className:"text-green-600 text-sm text-center",children:o})]})]})})]})}export{p as default};
