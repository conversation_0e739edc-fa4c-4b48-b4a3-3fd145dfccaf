{"private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build"}, "devDependencies": {"axios": "^1.6.4", "laravel-vite-plugin": "^1.0.0", "vite": "^5.0.0"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@inertiajs/react": "^2.0.11", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.8", "@vitejs/plugin-react": "^4.5.0", "autoprefixer": "^10.4.21", "postcss": "^8.5.4", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwindcss": "^4.1.8"}}