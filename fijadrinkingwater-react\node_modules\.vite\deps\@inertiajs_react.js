import {
  require_react
} from "./chunk-UGC3UZ7L.js";
import {
  axios_default
} from "./chunk-WELMVZTH.js";
import {
  __commonJS,
  __export,
  __toESM
} from "./chunk-G3PMV62Z.js";

// node_modules/es-errors/type.js
var require_type = __commonJS({
  "node_modules/es-errors/type.js"(exports, module) {
    "use strict";
    module.exports = TypeError;
  }
});

// (disabled):node_modules/object-inspect/util.inspect
var require_util = __commonJS({
  "(disabled):node_modules/object-inspect/util.inspect"() {
  }
});

// node_modules/object-inspect/index.js
var require_object_inspect = __commonJS({
  "node_modules/object-inspect/index.js"(exports, module) {
    var hasMap = typeof Map === "function" && Map.prototype;
    var mapSizeDescriptor = Object.getOwnPropertyDescriptor && hasMap ? Object.getOwnPropertyDescriptor(Map.prototype, "size") : null;
    var mapSize = hasMap && mapSizeDescriptor && typeof mapSizeDescriptor.get === "function" ? mapSizeDescriptor.get : null;
    var mapForEach = hasMap && Map.prototype.forEach;
    var hasSet = typeof Set === "function" && Set.prototype;
    var setSizeDescriptor = Object.getOwnPropertyDescriptor && hasSet ? Object.getOwnPropertyDescriptor(Set.prototype, "size") : null;
    var setSize = hasSet && setSizeDescriptor && typeof setSizeDescriptor.get === "function" ? setSizeDescriptor.get : null;
    var setForEach = hasSet && Set.prototype.forEach;
    var hasWeakMap = typeof WeakMap === "function" && WeakMap.prototype;
    var weakMapHas = hasWeakMap ? WeakMap.prototype.has : null;
    var hasWeakSet = typeof WeakSet === "function" && WeakSet.prototype;
    var weakSetHas = hasWeakSet ? WeakSet.prototype.has : null;
    var hasWeakRef = typeof WeakRef === "function" && WeakRef.prototype;
    var weakRefDeref = hasWeakRef ? WeakRef.prototype.deref : null;
    var booleanValueOf = Boolean.prototype.valueOf;
    var objectToString = Object.prototype.toString;
    var functionToString2 = Function.prototype.toString;
    var $match = String.prototype.match;
    var $slice = String.prototype.slice;
    var $replace = String.prototype.replace;
    var $toUpperCase = String.prototype.toUpperCase;
    var $toLowerCase = String.prototype.toLowerCase;
    var $test = RegExp.prototype.test;
    var $concat = Array.prototype.concat;
    var $join = Array.prototype.join;
    var $arrSlice = Array.prototype.slice;
    var $floor = Math.floor;
    var bigIntValueOf = typeof BigInt === "function" ? BigInt.prototype.valueOf : null;
    var gOPS = Object.getOwnPropertySymbols;
    var symToString = typeof Symbol === "function" && typeof Symbol.iterator === "symbol" ? Symbol.prototype.toString : null;
    var hasShammedSymbols = typeof Symbol === "function" && typeof Symbol.iterator === "object";
    var toStringTag = typeof Symbol === "function" && Symbol.toStringTag && (typeof Symbol.toStringTag === hasShammedSymbols ? "object" : "symbol") ? Symbol.toStringTag : null;
    var isEnumerable = Object.prototype.propertyIsEnumerable;
    var gPO = (typeof Reflect === "function" ? Reflect.getPrototypeOf : Object.getPrototypeOf) || ([].__proto__ === Array.prototype ? function(O3) {
      return O3.__proto__;
    } : null);
    function addNumericSeparator(num, str) {
      if (num === Infinity || num === -Infinity || num !== num || num && num > -1e3 && num < 1e3 || $test.call(/e/, str)) {
        return str;
      }
      var sepRegex = /[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;
      if (typeof num === "number") {
        var int = num < 0 ? -$floor(-num) : $floor(num);
        if (int !== num) {
          var intStr = String(int);
          var dec = $slice.call(str, intStr.length + 1);
          return $replace.call(intStr, sepRegex, "$&_") + "." + $replace.call($replace.call(dec, /([0-9]{3})/g, "$&_"), /_$/, "");
        }
      }
      return $replace.call(str, sepRegex, "$&_");
    }
    var utilInspect = require_util();
    var inspectCustom = utilInspect.custom;
    var inspectSymbol = isSymbol3(inspectCustom) ? inspectCustom : null;
    var quotes = {
      __proto__: null,
      "double": '"',
      single: "'"
    };
    var quoteREs = {
      __proto__: null,
      "double": /(["\\])/g,
      single: /(['\\])/g
    };
    module.exports = function inspect_(obj, options, depth, seen) {
      var opts = options || {};
      if (has2(opts, "quoteStyle") && !has2(quotes, opts.quoteStyle)) {
        throw new TypeError('option "quoteStyle" must be "single" or "double"');
      }
      if (has2(opts, "maxStringLength") && (typeof opts.maxStringLength === "number" ? opts.maxStringLength < 0 && opts.maxStringLength !== Infinity : opts.maxStringLength !== null)) {
        throw new TypeError('option "maxStringLength", if provided, must be a positive integer, Infinity, or `null`');
      }
      var customInspect = has2(opts, "customInspect") ? opts.customInspect : true;
      if (typeof customInspect !== "boolean" && customInspect !== "symbol") {
        throw new TypeError("option \"customInspect\", if provided, must be `true`, `false`, or `'symbol'`");
      }
      if (has2(opts, "indent") && opts.indent !== null && opts.indent !== "	" && !(parseInt(opts.indent, 10) === opts.indent && opts.indent > 0)) {
        throw new TypeError('option "indent" must be "\\t", an integer > 0, or `null`');
      }
      if (has2(opts, "numericSeparator") && typeof opts.numericSeparator !== "boolean") {
        throw new TypeError('option "numericSeparator", if provided, must be `true` or `false`');
      }
      var numericSeparator = opts.numericSeparator;
      if (typeof obj === "undefined") {
        return "undefined";
      }
      if (obj === null) {
        return "null";
      }
      if (typeof obj === "boolean") {
        return obj ? "true" : "false";
      }
      if (typeof obj === "string") {
        return inspectString(obj, opts);
      }
      if (typeof obj === "number") {
        if (obj === 0) {
          return Infinity / obj > 0 ? "0" : "-0";
        }
        var str = String(obj);
        return numericSeparator ? addNumericSeparator(obj, str) : str;
      }
      if (typeof obj === "bigint") {
        var bigIntStr = String(obj) + "n";
        return numericSeparator ? addNumericSeparator(obj, bigIntStr) : bigIntStr;
      }
      var maxDepth = typeof opts.depth === "undefined" ? 5 : opts.depth;
      if (typeof depth === "undefined") {
        depth = 0;
      }
      if (depth >= maxDepth && maxDepth > 0 && typeof obj === "object") {
        return isArray2(obj) ? "[Array]" : "[Object]";
      }
      var indent = getIndent(opts, depth);
      if (typeof seen === "undefined") {
        seen = [];
      } else if (indexOf2(seen, obj) >= 0) {
        return "[Circular]";
      }
      function inspect(value, from, noIndent) {
        if (from) {
          seen = $arrSlice.call(seen);
          seen.push(from);
        }
        if (noIndent) {
          var newOpts = {
            depth: opts.depth
          };
          if (has2(opts, "quoteStyle")) {
            newOpts.quoteStyle = opts.quoteStyle;
          }
          return inspect_(value, newOpts, depth + 1, seen);
        }
        return inspect_(value, opts, depth + 1, seen);
      }
      if (typeof obj === "function" && !isRegExp3(obj)) {
        var name = nameOf(obj);
        var keys2 = arrObjKeys(obj, inspect);
        return "[Function" + (name ? ": " + name : " (anonymous)") + "]" + (keys2.length > 0 ? " { " + $join.call(keys2, ", ") + " }" : "");
      }
      if (isSymbol3(obj)) {
        var symString = hasShammedSymbols ? $replace.call(String(obj), /^(Symbol\(.*\))_[^)]*$/, "$1") : symToString.call(obj);
        return typeof obj === "object" && !hasShammedSymbols ? markBoxed(symString) : symString;
      }
      if (isElement2(obj)) {
        var s = "<" + $toLowerCase.call(String(obj.nodeName));
        var attrs = obj.attributes || [];
        for (var i = 0; i < attrs.length; i++) {
          s += " " + attrs[i].name + "=" + wrapQuotes(quote(attrs[i].value), "double", opts);
        }
        s += ">";
        if (obj.childNodes && obj.childNodes.length) {
          s += "...";
        }
        s += "</" + $toLowerCase.call(String(obj.nodeName)) + ">";
        return s;
      }
      if (isArray2(obj)) {
        if (obj.length === 0) {
          return "[]";
        }
        var xs = arrObjKeys(obj, inspect);
        if (indent && !singleLineValues(xs)) {
          return "[" + indentedJoin(xs, indent) + "]";
        }
        return "[ " + $join.call(xs, ", ") + " ]";
      }
      if (isError3(obj)) {
        var parts = arrObjKeys(obj, inspect);
        if (!("cause" in Error.prototype) && "cause" in obj && !isEnumerable.call(obj, "cause")) {
          return "{ [" + String(obj) + "] " + $join.call($concat.call("[cause]: " + inspect(obj.cause), parts), ", ") + " }";
        }
        if (parts.length === 0) {
          return "[" + String(obj) + "]";
        }
        return "{ [" + String(obj) + "] " + $join.call(parts, ", ") + " }";
      }
      if (typeof obj === "object" && customInspect) {
        if (inspectSymbol && typeof obj[inspectSymbol] === "function" && utilInspect) {
          return utilInspect(obj, { depth: maxDepth - depth });
        } else if (customInspect !== "symbol" && typeof obj.inspect === "function") {
          return obj.inspect();
        }
      }
      if (isMap3(obj)) {
        var mapParts = [];
        if (mapForEach) {
          mapForEach.call(obj, function(value, key) {
            mapParts.push(inspect(key, obj, true) + " => " + inspect(value, obj));
          });
        }
        return collectionOf("Map", mapSize.call(obj), mapParts, indent);
      }
      if (isSet3(obj)) {
        var setParts = [];
        if (setForEach) {
          setForEach.call(obj, function(value) {
            setParts.push(inspect(value, obj));
          });
        }
        return collectionOf("Set", setSize.call(obj), setParts, indent);
      }
      if (isWeakMap3(obj)) {
        return weakCollectionOf("WeakMap");
      }
      if (isWeakSet3(obj)) {
        return weakCollectionOf("WeakSet");
      }
      if (isWeakRef(obj)) {
        return weakCollectionOf("WeakRef");
      }
      if (isNumber2(obj)) {
        return markBoxed(inspect(Number(obj)));
      }
      if (isBigInt(obj)) {
        return markBoxed(inspect(bigIntValueOf.call(obj)));
      }
      if (isBoolean3(obj)) {
        return markBoxed(booleanValueOf.call(obj));
      }
      if (isString3(obj)) {
        return markBoxed(inspect(String(obj)));
      }
      if (typeof window !== "undefined" && obj === window) {
        return "{ [object Window] }";
      }
      if (typeof globalThis !== "undefined" && obj === globalThis || typeof global !== "undefined" && obj === global) {
        return "{ [object globalThis] }";
      }
      if (!isDate3(obj) && !isRegExp3(obj)) {
        var ys = arrObjKeys(obj, inspect);
        var isPlainObject3 = gPO ? gPO(obj) === Object.prototype : obj instanceof Object || obj.constructor === Object;
        var protoTag = obj instanceof Object ? "" : "null prototype";
        var stringTag2 = !isPlainObject3 && toStringTag && Object(obj) === obj && toStringTag in obj ? $slice.call(toStr(obj), 8, -1) : protoTag ? "Object" : "";
        var constructorTag = isPlainObject3 || typeof obj.constructor !== "function" ? "" : obj.constructor.name ? obj.constructor.name + " " : "";
        var tag = constructorTag + (stringTag2 || protoTag ? "[" + $join.call($concat.call([], stringTag2 || [], protoTag || []), ": ") + "] " : "");
        if (ys.length === 0) {
          return tag + "{}";
        }
        if (indent) {
          return tag + "{" + indentedJoin(ys, indent) + "}";
        }
        return tag + "{ " + $join.call(ys, ", ") + " }";
      }
      return String(obj);
    };
    function wrapQuotes(s, defaultStyle, opts) {
      var style = opts.quoteStyle || defaultStyle;
      var quoteChar = quotes[style];
      return quoteChar + s + quoteChar;
    }
    function quote(s) {
      return $replace.call(String(s), /"/g, "&quot;");
    }
    function canTrustToString(obj) {
      return !toStringTag || !(typeof obj === "object" && (toStringTag in obj || typeof obj[toStringTag] !== "undefined"));
    }
    function isArray2(obj) {
      return toStr(obj) === "[object Array]" && canTrustToString(obj);
    }
    function isDate3(obj) {
      return toStr(obj) === "[object Date]" && canTrustToString(obj);
    }
    function isRegExp3(obj) {
      return toStr(obj) === "[object RegExp]" && canTrustToString(obj);
    }
    function isError3(obj) {
      return toStr(obj) === "[object Error]" && canTrustToString(obj);
    }
    function isString3(obj) {
      return toStr(obj) === "[object String]" && canTrustToString(obj);
    }
    function isNumber2(obj) {
      return toStr(obj) === "[object Number]" && canTrustToString(obj);
    }
    function isBoolean3(obj) {
      return toStr(obj) === "[object Boolean]" && canTrustToString(obj);
    }
    function isSymbol3(obj) {
      if (hasShammedSymbols) {
        return obj && typeof obj === "object" && obj instanceof Symbol;
      }
      if (typeof obj === "symbol") {
        return true;
      }
      if (!obj || typeof obj !== "object" || !symToString) {
        return false;
      }
      try {
        symToString.call(obj);
        return true;
      } catch (e) {
      }
      return false;
    }
    function isBigInt(obj) {
      if (!obj || typeof obj !== "object" || !bigIntValueOf) {
        return false;
      }
      try {
        bigIntValueOf.call(obj);
        return true;
      } catch (e) {
      }
      return false;
    }
    var hasOwn = Object.prototype.hasOwnProperty || function(key) {
      return key in this;
    };
    function has2(obj, key) {
      return hasOwn.call(obj, key);
    }
    function toStr(obj) {
      return objectToString.call(obj);
    }
    function nameOf(f2) {
      if (f2.name) {
        return f2.name;
      }
      var m2 = $match.call(functionToString2.call(f2), /^function\s*([\w$]+)/);
      if (m2) {
        return m2[1];
      }
      return null;
    }
    function indexOf2(xs, x3) {
      if (xs.indexOf) {
        return xs.indexOf(x3);
      }
      for (var i = 0, l = xs.length; i < l; i++) {
        if (xs[i] === x3) {
          return i;
        }
      }
      return -1;
    }
    function isMap3(x3) {
      if (!mapSize || !x3 || typeof x3 !== "object") {
        return false;
      }
      try {
        mapSize.call(x3);
        try {
          setSize.call(x3);
        } catch (s) {
          return true;
        }
        return x3 instanceof Map;
      } catch (e) {
      }
      return false;
    }
    function isWeakMap3(x3) {
      if (!weakMapHas || !x3 || typeof x3 !== "object") {
        return false;
      }
      try {
        weakMapHas.call(x3, weakMapHas);
        try {
          weakSetHas.call(x3, weakSetHas);
        } catch (s) {
          return true;
        }
        return x3 instanceof WeakMap;
      } catch (e) {
      }
      return false;
    }
    function isWeakRef(x3) {
      if (!weakRefDeref || !x3 || typeof x3 !== "object") {
        return false;
      }
      try {
        weakRefDeref.call(x3);
        return true;
      } catch (e) {
      }
      return false;
    }
    function isSet3(x3) {
      if (!setSize || !x3 || typeof x3 !== "object") {
        return false;
      }
      try {
        setSize.call(x3);
        try {
          mapSize.call(x3);
        } catch (m2) {
          return true;
        }
        return x3 instanceof Set;
      } catch (e) {
      }
      return false;
    }
    function isWeakSet3(x3) {
      if (!weakSetHas || !x3 || typeof x3 !== "object") {
        return false;
      }
      try {
        weakSetHas.call(x3, weakSetHas);
        try {
          weakMapHas.call(x3, weakMapHas);
        } catch (s) {
          return true;
        }
        return x3 instanceof WeakSet;
      } catch (e) {
      }
      return false;
    }
    function isElement2(x3) {
      if (!x3 || typeof x3 !== "object") {
        return false;
      }
      if (typeof HTMLElement !== "undefined" && x3 instanceof HTMLElement) {
        return true;
      }
      return typeof x3.nodeName === "string" && typeof x3.getAttribute === "function";
    }
    function inspectString(str, opts) {
      if (str.length > opts.maxStringLength) {
        var remaining = str.length - opts.maxStringLength;
        var trailer = "... " + remaining + " more character" + (remaining > 1 ? "s" : "");
        return inspectString($slice.call(str, 0, opts.maxStringLength), opts) + trailer;
      }
      var quoteRE = quoteREs[opts.quoteStyle || "single"];
      quoteRE.lastIndex = 0;
      var s = $replace.call($replace.call(str, quoteRE, "\\$1"), /[\x00-\x1f]/g, lowbyte);
      return wrapQuotes(s, "single", opts);
    }
    function lowbyte(c) {
      var n2 = c.charCodeAt(0);
      var x3 = {
        8: "b",
        9: "t",
        10: "n",
        12: "f",
        13: "r"
      }[n2];
      if (x3) {
        return "\\" + x3;
      }
      return "\\x" + (n2 < 16 ? "0" : "") + $toUpperCase.call(n2.toString(16));
    }
    function markBoxed(str) {
      return "Object(" + str + ")";
    }
    function weakCollectionOf(type) {
      return type + " { ? }";
    }
    function collectionOf(type, size2, entries, indent) {
      var joinedEntries = indent ? indentedJoin(entries, indent) : $join.call(entries, ", ");
      return type + " (" + size2 + ") {" + joinedEntries + "}";
    }
    function singleLineValues(xs) {
      for (var i = 0; i < xs.length; i++) {
        if (indexOf2(xs[i], "\n") >= 0) {
          return false;
        }
      }
      return true;
    }
    function getIndent(opts, depth) {
      var baseIndent;
      if (opts.indent === "	") {
        baseIndent = "	";
      } else if (typeof opts.indent === "number" && opts.indent > 0) {
        baseIndent = $join.call(Array(opts.indent + 1), " ");
      } else {
        return null;
      }
      return {
        base: baseIndent,
        prev: $join.call(Array(depth + 1), baseIndent)
      };
    }
    function indentedJoin(xs, indent) {
      if (xs.length === 0) {
        return "";
      }
      var lineJoiner = "\n" + indent.prev + indent.base;
      return lineJoiner + $join.call(xs, "," + lineJoiner) + "\n" + indent.prev;
    }
    function arrObjKeys(obj, inspect) {
      var isArr = isArray2(obj);
      var xs = [];
      if (isArr) {
        xs.length = obj.length;
        for (var i = 0; i < obj.length; i++) {
          xs[i] = has2(obj, i) ? inspect(obj[i], obj) : "";
        }
      }
      var syms = typeof gOPS === "function" ? gOPS(obj) : [];
      var symMap;
      if (hasShammedSymbols) {
        symMap = {};
        for (var k2 = 0; k2 < syms.length; k2++) {
          symMap["$" + syms[k2]] = syms[k2];
        }
      }
      for (var key in obj) {
        if (!has2(obj, key)) {
          continue;
        }
        if (isArr && String(Number(key)) === key && key < obj.length) {
          continue;
        }
        if (hasShammedSymbols && symMap["$" + key] instanceof Symbol) {
          continue;
        } else if ($test.call(/[^\w$]/, key)) {
          xs.push(inspect(key, obj) + ": " + inspect(obj[key], obj));
        } else {
          xs.push(key + ": " + inspect(obj[key], obj));
        }
      }
      if (typeof gOPS === "function") {
        for (var j2 = 0; j2 < syms.length; j2++) {
          if (isEnumerable.call(obj, syms[j2])) {
            xs.push("[" + inspect(syms[j2]) + "]: " + inspect(obj[syms[j2]], obj));
          }
        }
      }
      return xs;
    }
  }
});

// node_modules/side-channel-list/index.js
var require_side_channel_list = __commonJS({
  "node_modules/side-channel-list/index.js"(exports, module) {
    "use strict";
    var inspect = require_object_inspect();
    var $TypeError = require_type();
    var listGetNode = function(list, key, isDelete) {
      var prev = list;
      var curr;
      for (; (curr = prev.next) != null; prev = curr) {
        if (curr.key === key) {
          prev.next = curr.next;
          if (!isDelete) {
            curr.next = /** @type {NonNullable<typeof list.next>} */
            list.next;
            list.next = curr;
          }
          return curr;
        }
      }
    };
    var listGet = function(objects, key) {
      if (!objects) {
        return void 0;
      }
      var node = listGetNode(objects, key);
      return node && node.value;
    };
    var listSet = function(objects, key, value) {
      var node = listGetNode(objects, key);
      if (node) {
        node.value = value;
      } else {
        objects.next = /** @type {import('./list.d.ts').ListNode<typeof value, typeof key>} */
        {
          // eslint-disable-line no-param-reassign, no-extra-parens
          key,
          next: objects.next,
          value
        };
      }
    };
    var listHas = function(objects, key) {
      if (!objects) {
        return false;
      }
      return !!listGetNode(objects, key);
    };
    var listDelete = function(objects, key) {
      if (objects) {
        return listGetNode(objects, key, true);
      }
    };
    module.exports = function getSideChannelList() {
      var $o;
      var channel = {
        assert: function(key) {
          if (!channel.has(key)) {
            throw new $TypeError("Side channel does not contain " + inspect(key));
          }
        },
        "delete": function(key) {
          var root = $o && $o.next;
          var deletedNode = listDelete($o, key);
          if (deletedNode && root && root === deletedNode) {
            $o = void 0;
          }
          return !!deletedNode;
        },
        get: function(key) {
          return listGet($o, key);
        },
        has: function(key) {
          return listHas($o, key);
        },
        set: function(key, value) {
          if (!$o) {
            $o = {
              next: void 0
            };
          }
          listSet(
            /** @type {NonNullable<typeof $o>} */
            $o,
            key,
            value
          );
        }
      };
      return channel;
    };
  }
});

// node_modules/es-object-atoms/index.js
var require_es_object_atoms = __commonJS({
  "node_modules/es-object-atoms/index.js"(exports, module) {
    "use strict";
    module.exports = Object;
  }
});

// node_modules/es-errors/index.js
var require_es_errors = __commonJS({
  "node_modules/es-errors/index.js"(exports, module) {
    "use strict";
    module.exports = Error;
  }
});

// node_modules/es-errors/eval.js
var require_eval = __commonJS({
  "node_modules/es-errors/eval.js"(exports, module) {
    "use strict";
    module.exports = EvalError;
  }
});

// node_modules/es-errors/range.js
var require_range = __commonJS({
  "node_modules/es-errors/range.js"(exports, module) {
    "use strict";
    module.exports = RangeError;
  }
});

// node_modules/es-errors/ref.js
var require_ref = __commonJS({
  "node_modules/es-errors/ref.js"(exports, module) {
    "use strict";
    module.exports = ReferenceError;
  }
});

// node_modules/es-errors/syntax.js
var require_syntax = __commonJS({
  "node_modules/es-errors/syntax.js"(exports, module) {
    "use strict";
    module.exports = SyntaxError;
  }
});

// node_modules/es-errors/uri.js
var require_uri = __commonJS({
  "node_modules/es-errors/uri.js"(exports, module) {
    "use strict";
    module.exports = URIError;
  }
});

// node_modules/math-intrinsics/abs.js
var require_abs = __commonJS({
  "node_modules/math-intrinsics/abs.js"(exports, module) {
    "use strict";
    module.exports = Math.abs;
  }
});

// node_modules/math-intrinsics/floor.js
var require_floor = __commonJS({
  "node_modules/math-intrinsics/floor.js"(exports, module) {
    "use strict";
    module.exports = Math.floor;
  }
});

// node_modules/math-intrinsics/max.js
var require_max = __commonJS({
  "node_modules/math-intrinsics/max.js"(exports, module) {
    "use strict";
    module.exports = Math.max;
  }
});

// node_modules/math-intrinsics/min.js
var require_min = __commonJS({
  "node_modules/math-intrinsics/min.js"(exports, module) {
    "use strict";
    module.exports = Math.min;
  }
});

// node_modules/math-intrinsics/pow.js
var require_pow = __commonJS({
  "node_modules/math-intrinsics/pow.js"(exports, module) {
    "use strict";
    module.exports = Math.pow;
  }
});

// node_modules/math-intrinsics/round.js
var require_round = __commonJS({
  "node_modules/math-intrinsics/round.js"(exports, module) {
    "use strict";
    module.exports = Math.round;
  }
});

// node_modules/math-intrinsics/isNaN.js
var require_isNaN = __commonJS({
  "node_modules/math-intrinsics/isNaN.js"(exports, module) {
    "use strict";
    module.exports = Number.isNaN || function isNaN3(a2) {
      return a2 !== a2;
    };
  }
});

// node_modules/math-intrinsics/sign.js
var require_sign = __commonJS({
  "node_modules/math-intrinsics/sign.js"(exports, module) {
    "use strict";
    var $isNaN = require_isNaN();
    module.exports = function sign(number) {
      if ($isNaN(number) || number === 0) {
        return number;
      }
      return number < 0 ? -1 : 1;
    };
  }
});

// node_modules/gopd/gOPD.js
var require_gOPD = __commonJS({
  "node_modules/gopd/gOPD.js"(exports, module) {
    "use strict";
    module.exports = Object.getOwnPropertyDescriptor;
  }
});

// node_modules/gopd/index.js
var require_gopd = __commonJS({
  "node_modules/gopd/index.js"(exports, module) {
    "use strict";
    var $gOPD = require_gOPD();
    if ($gOPD) {
      try {
        $gOPD([], "length");
      } catch (e) {
        $gOPD = null;
      }
    }
    module.exports = $gOPD;
  }
});

// node_modules/es-define-property/index.js
var require_es_define_property = __commonJS({
  "node_modules/es-define-property/index.js"(exports, module) {
    "use strict";
    var $defineProperty = Object.defineProperty || false;
    if ($defineProperty) {
      try {
        $defineProperty({}, "a", { value: 1 });
      } catch (e) {
        $defineProperty = false;
      }
    }
    module.exports = $defineProperty;
  }
});

// node_modules/has-symbols/shams.js
var require_shams = __commonJS({
  "node_modules/has-symbols/shams.js"(exports, module) {
    "use strict";
    module.exports = function hasSymbols() {
      if (typeof Symbol !== "function" || typeof Object.getOwnPropertySymbols !== "function") {
        return false;
      }
      if (typeof Symbol.iterator === "symbol") {
        return true;
      }
      var obj = {};
      var sym = Symbol("test");
      var symObj = Object(sym);
      if (typeof sym === "string") {
        return false;
      }
      if (Object.prototype.toString.call(sym) !== "[object Symbol]") {
        return false;
      }
      if (Object.prototype.toString.call(symObj) !== "[object Symbol]") {
        return false;
      }
      var symVal = 42;
      obj[sym] = symVal;
      for (var _3 in obj) {
        return false;
      }
      if (typeof Object.keys === "function" && Object.keys(obj).length !== 0) {
        return false;
      }
      if (typeof Object.getOwnPropertyNames === "function" && Object.getOwnPropertyNames(obj).length !== 0) {
        return false;
      }
      var syms = Object.getOwnPropertySymbols(obj);
      if (syms.length !== 1 || syms[0] !== sym) {
        return false;
      }
      if (!Object.prototype.propertyIsEnumerable.call(obj, sym)) {
        return false;
      }
      if (typeof Object.getOwnPropertyDescriptor === "function") {
        var descriptor = (
          /** @type {PropertyDescriptor} */
          Object.getOwnPropertyDescriptor(obj, sym)
        );
        if (descriptor.value !== symVal || descriptor.enumerable !== true) {
          return false;
        }
      }
      return true;
    };
  }
});

// node_modules/has-symbols/index.js
var require_has_symbols = __commonJS({
  "node_modules/has-symbols/index.js"(exports, module) {
    "use strict";
    var origSymbol = typeof Symbol !== "undefined" && Symbol;
    var hasSymbolSham = require_shams();
    module.exports = function hasNativeSymbols() {
      if (typeof origSymbol !== "function") {
        return false;
      }
      if (typeof Symbol !== "function") {
        return false;
      }
      if (typeof origSymbol("foo") !== "symbol") {
        return false;
      }
      if (typeof Symbol("bar") !== "symbol") {
        return false;
      }
      return hasSymbolSham();
    };
  }
});

// node_modules/get-proto/Reflect.getPrototypeOf.js
var require_Reflect_getPrototypeOf = __commonJS({
  "node_modules/get-proto/Reflect.getPrototypeOf.js"(exports, module) {
    "use strict";
    module.exports = typeof Reflect !== "undefined" && Reflect.getPrototypeOf || null;
  }
});

// node_modules/get-proto/Object.getPrototypeOf.js
var require_Object_getPrototypeOf = __commonJS({
  "node_modules/get-proto/Object.getPrototypeOf.js"(exports, module) {
    "use strict";
    var $Object = require_es_object_atoms();
    module.exports = $Object.getPrototypeOf || null;
  }
});

// node_modules/function-bind/implementation.js
var require_implementation = __commonJS({
  "node_modules/function-bind/implementation.js"(exports, module) {
    "use strict";
    var ERROR_MESSAGE = "Function.prototype.bind called on incompatible ";
    var toStr = Object.prototype.toString;
    var max2 = Math.max;
    var funcType = "[object Function]";
    var concatty = function concatty2(a2, b2) {
      var arr = [];
      for (var i = 0; i < a2.length; i += 1) {
        arr[i] = a2[i];
      }
      for (var j2 = 0; j2 < b2.length; j2 += 1) {
        arr[j2 + a2.length] = b2[j2];
      }
      return arr;
    };
    var slicy = function slicy2(arrLike, offset) {
      var arr = [];
      for (var i = offset || 0, j2 = 0; i < arrLike.length; i += 1, j2 += 1) {
        arr[j2] = arrLike[i];
      }
      return arr;
    };
    var joiny = function(arr, joiner) {
      var str = "";
      for (var i = 0; i < arr.length; i += 1) {
        str += arr[i];
        if (i + 1 < arr.length) {
          str += joiner;
        }
      }
      return str;
    };
    module.exports = function bind2(that) {
      var target = this;
      if (typeof target !== "function" || toStr.apply(target) !== funcType) {
        throw new TypeError(ERROR_MESSAGE + target);
      }
      var args = slicy(arguments, 1);
      var bound;
      var binder = function() {
        if (this instanceof bound) {
          var result2 = target.apply(
            this,
            concatty(args, arguments)
          );
          if (Object(result2) === result2) {
            return result2;
          }
          return this;
        }
        return target.apply(
          that,
          concatty(args, arguments)
        );
      };
      var boundLength = max2(0, target.length - args.length);
      var boundArgs = [];
      for (var i = 0; i < boundLength; i++) {
        boundArgs[i] = "$" + i;
      }
      bound = Function("binder", "return function (" + joiny(boundArgs, ",") + "){ return binder.apply(this,arguments); }")(binder);
      if (target.prototype) {
        var Empty = function Empty2() {
        };
        Empty.prototype = target.prototype;
        bound.prototype = new Empty();
        Empty.prototype = null;
      }
      return bound;
    };
  }
});

// node_modules/function-bind/index.js
var require_function_bind = __commonJS({
  "node_modules/function-bind/index.js"(exports, module) {
    "use strict";
    var implementation = require_implementation();
    module.exports = Function.prototype.bind || implementation;
  }
});

// node_modules/call-bind-apply-helpers/functionCall.js
var require_functionCall = __commonJS({
  "node_modules/call-bind-apply-helpers/functionCall.js"(exports, module) {
    "use strict";
    module.exports = Function.prototype.call;
  }
});

// node_modules/call-bind-apply-helpers/functionApply.js
var require_functionApply = __commonJS({
  "node_modules/call-bind-apply-helpers/functionApply.js"(exports, module) {
    "use strict";
    module.exports = Function.prototype.apply;
  }
});

// node_modules/call-bind-apply-helpers/reflectApply.js
var require_reflectApply = __commonJS({
  "node_modules/call-bind-apply-helpers/reflectApply.js"(exports, module) {
    "use strict";
    module.exports = typeof Reflect !== "undefined" && Reflect && Reflect.apply;
  }
});

// node_modules/call-bind-apply-helpers/actualApply.js
var require_actualApply = __commonJS({
  "node_modules/call-bind-apply-helpers/actualApply.js"(exports, module) {
    "use strict";
    var bind2 = require_function_bind();
    var $apply = require_functionApply();
    var $call = require_functionCall();
    var $reflectApply = require_reflectApply();
    module.exports = $reflectApply || bind2.call($call, $apply);
  }
});

// node_modules/call-bind-apply-helpers/index.js
var require_call_bind_apply_helpers = __commonJS({
  "node_modules/call-bind-apply-helpers/index.js"(exports, module) {
    "use strict";
    var bind2 = require_function_bind();
    var $TypeError = require_type();
    var $call = require_functionCall();
    var $actualApply = require_actualApply();
    module.exports = function callBindBasic(args) {
      if (args.length < 1 || typeof args[0] !== "function") {
        throw new $TypeError("a function is required");
      }
      return $actualApply(bind2, $call, args);
    };
  }
});

// node_modules/dunder-proto/get.js
var require_get = __commonJS({
  "node_modules/dunder-proto/get.js"(exports, module) {
    "use strict";
    var callBind = require_call_bind_apply_helpers();
    var gOPD = require_gopd();
    var hasProtoAccessor;
    try {
      hasProtoAccessor = /** @type {{ __proto__?: typeof Array.prototype }} */
      [].__proto__ === Array.prototype;
    } catch (e) {
      if (!e || typeof e !== "object" || !("code" in e) || e.code !== "ERR_PROTO_ACCESS") {
        throw e;
      }
    }
    var desc = !!hasProtoAccessor && gOPD && gOPD(
      Object.prototype,
      /** @type {keyof typeof Object.prototype} */
      "__proto__"
    );
    var $Object = Object;
    var $getPrototypeOf = $Object.getPrototypeOf;
    module.exports = desc && typeof desc.get === "function" ? callBind([desc.get]) : typeof $getPrototypeOf === "function" ? (
      /** @type {import('./get')} */
      function getDunder(value) {
        return $getPrototypeOf(value == null ? value : $Object(value));
      }
    ) : false;
  }
});

// node_modules/get-proto/index.js
var require_get_proto = __commonJS({
  "node_modules/get-proto/index.js"(exports, module) {
    "use strict";
    var reflectGetProto = require_Reflect_getPrototypeOf();
    var originalGetProto = require_Object_getPrototypeOf();
    var getDunderProto = require_get();
    module.exports = reflectGetProto ? function getProto(O3) {
      return reflectGetProto(O3);
    } : originalGetProto ? function getProto(O3) {
      if (!O3 || typeof O3 !== "object" && typeof O3 !== "function") {
        throw new TypeError("getProto: not an object");
      }
      return originalGetProto(O3);
    } : getDunderProto ? function getProto(O3) {
      return getDunderProto(O3);
    } : null;
  }
});

// node_modules/hasown/index.js
var require_hasown = __commonJS({
  "node_modules/hasown/index.js"(exports, module) {
    "use strict";
    var call = Function.prototype.call;
    var $hasOwn = Object.prototype.hasOwnProperty;
    var bind2 = require_function_bind();
    module.exports = bind2.call(call, $hasOwn);
  }
});

// node_modules/get-intrinsic/index.js
var require_get_intrinsic = __commonJS({
  "node_modules/get-intrinsic/index.js"(exports, module) {
    "use strict";
    var undefined2;
    var $Object = require_es_object_atoms();
    var $Error = require_es_errors();
    var $EvalError = require_eval();
    var $RangeError = require_range();
    var $ReferenceError = require_ref();
    var $SyntaxError = require_syntax();
    var $TypeError = require_type();
    var $URIError = require_uri();
    var abs = require_abs();
    var floor2 = require_floor();
    var max2 = require_max();
    var min2 = require_min();
    var pow = require_pow();
    var round3 = require_round();
    var sign = require_sign();
    var $Function = Function;
    var getEvalledConstructor = function(expressionSyntax) {
      try {
        return $Function('"use strict"; return (' + expressionSyntax + ").constructor;")();
      } catch (e) {
      }
    };
    var $gOPD = require_gopd();
    var $defineProperty = require_es_define_property();
    var throwTypeError = function() {
      throw new $TypeError();
    };
    var ThrowTypeError = $gOPD ? function() {
      try {
        arguments.callee;
        return throwTypeError;
      } catch (calleeThrows) {
        try {
          return $gOPD(arguments, "callee").get;
        } catch (gOPDthrows) {
          return throwTypeError;
        }
      }
    }() : throwTypeError;
    var hasSymbols = require_has_symbols()();
    var getProto = require_get_proto();
    var $ObjectGPO = require_Object_getPrototypeOf();
    var $ReflectGPO = require_Reflect_getPrototypeOf();
    var $apply = require_functionApply();
    var $call = require_functionCall();
    var needsEval = {};
    var TypedArray = typeof Uint8Array === "undefined" || !getProto ? undefined2 : getProto(Uint8Array);
    var INTRINSICS = {
      __proto__: null,
      "%AggregateError%": typeof AggregateError === "undefined" ? undefined2 : AggregateError,
      "%Array%": Array,
      "%ArrayBuffer%": typeof ArrayBuffer === "undefined" ? undefined2 : ArrayBuffer,
      "%ArrayIteratorPrototype%": hasSymbols && getProto ? getProto([][Symbol.iterator]()) : undefined2,
      "%AsyncFromSyncIteratorPrototype%": undefined2,
      "%AsyncFunction%": needsEval,
      "%AsyncGenerator%": needsEval,
      "%AsyncGeneratorFunction%": needsEval,
      "%AsyncIteratorPrototype%": needsEval,
      "%Atomics%": typeof Atomics === "undefined" ? undefined2 : Atomics,
      "%BigInt%": typeof BigInt === "undefined" ? undefined2 : BigInt,
      "%BigInt64Array%": typeof BigInt64Array === "undefined" ? undefined2 : BigInt64Array,
      "%BigUint64Array%": typeof BigUint64Array === "undefined" ? undefined2 : BigUint64Array,
      "%Boolean%": Boolean,
      "%DataView%": typeof DataView === "undefined" ? undefined2 : DataView,
      "%Date%": Date,
      "%decodeURI%": decodeURI,
      "%decodeURIComponent%": decodeURIComponent,
      "%encodeURI%": encodeURI,
      "%encodeURIComponent%": encodeURIComponent,
      "%Error%": $Error,
      "%eval%": eval,
      // eslint-disable-line no-eval
      "%EvalError%": $EvalError,
      "%Float16Array%": typeof Float16Array === "undefined" ? undefined2 : Float16Array,
      "%Float32Array%": typeof Float32Array === "undefined" ? undefined2 : Float32Array,
      "%Float64Array%": typeof Float64Array === "undefined" ? undefined2 : Float64Array,
      "%FinalizationRegistry%": typeof FinalizationRegistry === "undefined" ? undefined2 : FinalizationRegistry,
      "%Function%": $Function,
      "%GeneratorFunction%": needsEval,
      "%Int8Array%": typeof Int8Array === "undefined" ? undefined2 : Int8Array,
      "%Int16Array%": typeof Int16Array === "undefined" ? undefined2 : Int16Array,
      "%Int32Array%": typeof Int32Array === "undefined" ? undefined2 : Int32Array,
      "%isFinite%": isFinite,
      "%isNaN%": isNaN,
      "%IteratorPrototype%": hasSymbols && getProto ? getProto(getProto([][Symbol.iterator]())) : undefined2,
      "%JSON%": typeof JSON === "object" ? JSON : undefined2,
      "%Map%": typeof Map === "undefined" ? undefined2 : Map,
      "%MapIteratorPrototype%": typeof Map === "undefined" || !hasSymbols || !getProto ? undefined2 : getProto((/* @__PURE__ */ new Map())[Symbol.iterator]()),
      "%Math%": Math,
      "%Number%": Number,
      "%Object%": $Object,
      "%Object.getOwnPropertyDescriptor%": $gOPD,
      "%parseFloat%": parseFloat,
      "%parseInt%": parseInt,
      "%Promise%": typeof Promise === "undefined" ? undefined2 : Promise,
      "%Proxy%": typeof Proxy === "undefined" ? undefined2 : Proxy,
      "%RangeError%": $RangeError,
      "%ReferenceError%": $ReferenceError,
      "%Reflect%": typeof Reflect === "undefined" ? undefined2 : Reflect,
      "%RegExp%": RegExp,
      "%Set%": typeof Set === "undefined" ? undefined2 : Set,
      "%SetIteratorPrototype%": typeof Set === "undefined" || !hasSymbols || !getProto ? undefined2 : getProto((/* @__PURE__ */ new Set())[Symbol.iterator]()),
      "%SharedArrayBuffer%": typeof SharedArrayBuffer === "undefined" ? undefined2 : SharedArrayBuffer,
      "%String%": String,
      "%StringIteratorPrototype%": hasSymbols && getProto ? getProto(""[Symbol.iterator]()) : undefined2,
      "%Symbol%": hasSymbols ? Symbol : undefined2,
      "%SyntaxError%": $SyntaxError,
      "%ThrowTypeError%": ThrowTypeError,
      "%TypedArray%": TypedArray,
      "%TypeError%": $TypeError,
      "%Uint8Array%": typeof Uint8Array === "undefined" ? undefined2 : Uint8Array,
      "%Uint8ClampedArray%": typeof Uint8ClampedArray === "undefined" ? undefined2 : Uint8ClampedArray,
      "%Uint16Array%": typeof Uint16Array === "undefined" ? undefined2 : Uint16Array,
      "%Uint32Array%": typeof Uint32Array === "undefined" ? undefined2 : Uint32Array,
      "%URIError%": $URIError,
      "%WeakMap%": typeof WeakMap === "undefined" ? undefined2 : WeakMap,
      "%WeakRef%": typeof WeakRef === "undefined" ? undefined2 : WeakRef,
      "%WeakSet%": typeof WeakSet === "undefined" ? undefined2 : WeakSet,
      "%Function.prototype.call%": $call,
      "%Function.prototype.apply%": $apply,
      "%Object.defineProperty%": $defineProperty,
      "%Object.getPrototypeOf%": $ObjectGPO,
      "%Math.abs%": abs,
      "%Math.floor%": floor2,
      "%Math.max%": max2,
      "%Math.min%": min2,
      "%Math.pow%": pow,
      "%Math.round%": round3,
      "%Math.sign%": sign,
      "%Reflect.getPrototypeOf%": $ReflectGPO
    };
    if (getProto) {
      try {
        null.error;
      } catch (e) {
        errorProto = getProto(getProto(e));
        INTRINSICS["%Error.prototype%"] = errorProto;
      }
    }
    var errorProto;
    var doEval = function doEval2(name) {
      var value;
      if (name === "%AsyncFunction%") {
        value = getEvalledConstructor("async function () {}");
      } else if (name === "%GeneratorFunction%") {
        value = getEvalledConstructor("function* () {}");
      } else if (name === "%AsyncGeneratorFunction%") {
        value = getEvalledConstructor("async function* () {}");
      } else if (name === "%AsyncGenerator%") {
        var fn = doEval2("%AsyncGeneratorFunction%");
        if (fn) {
          value = fn.prototype;
        }
      } else if (name === "%AsyncIteratorPrototype%") {
        var gen = doEval2("%AsyncGenerator%");
        if (gen && getProto) {
          value = getProto(gen.prototype);
        }
      }
      INTRINSICS[name] = value;
      return value;
    };
    var LEGACY_ALIASES = {
      __proto__: null,
      "%ArrayBufferPrototype%": ["ArrayBuffer", "prototype"],
      "%ArrayPrototype%": ["Array", "prototype"],
      "%ArrayProto_entries%": ["Array", "prototype", "entries"],
      "%ArrayProto_forEach%": ["Array", "prototype", "forEach"],
      "%ArrayProto_keys%": ["Array", "prototype", "keys"],
      "%ArrayProto_values%": ["Array", "prototype", "values"],
      "%AsyncFunctionPrototype%": ["AsyncFunction", "prototype"],
      "%AsyncGenerator%": ["AsyncGeneratorFunction", "prototype"],
      "%AsyncGeneratorPrototype%": ["AsyncGeneratorFunction", "prototype", "prototype"],
      "%BooleanPrototype%": ["Boolean", "prototype"],
      "%DataViewPrototype%": ["DataView", "prototype"],
      "%DatePrototype%": ["Date", "prototype"],
      "%ErrorPrototype%": ["Error", "prototype"],
      "%EvalErrorPrototype%": ["EvalError", "prototype"],
      "%Float32ArrayPrototype%": ["Float32Array", "prototype"],
      "%Float64ArrayPrototype%": ["Float64Array", "prototype"],
      "%FunctionPrototype%": ["Function", "prototype"],
      "%Generator%": ["GeneratorFunction", "prototype"],
      "%GeneratorPrototype%": ["GeneratorFunction", "prototype", "prototype"],
      "%Int8ArrayPrototype%": ["Int8Array", "prototype"],
      "%Int16ArrayPrototype%": ["Int16Array", "prototype"],
      "%Int32ArrayPrototype%": ["Int32Array", "prototype"],
      "%JSONParse%": ["JSON", "parse"],
      "%JSONStringify%": ["JSON", "stringify"],
      "%MapPrototype%": ["Map", "prototype"],
      "%NumberPrototype%": ["Number", "prototype"],
      "%ObjectPrototype%": ["Object", "prototype"],
      "%ObjProto_toString%": ["Object", "prototype", "toString"],
      "%ObjProto_valueOf%": ["Object", "prototype", "valueOf"],
      "%PromisePrototype%": ["Promise", "prototype"],
      "%PromiseProto_then%": ["Promise", "prototype", "then"],
      "%Promise_all%": ["Promise", "all"],
      "%Promise_reject%": ["Promise", "reject"],
      "%Promise_resolve%": ["Promise", "resolve"],
      "%RangeErrorPrototype%": ["RangeError", "prototype"],
      "%ReferenceErrorPrototype%": ["ReferenceError", "prototype"],
      "%RegExpPrototype%": ["RegExp", "prototype"],
      "%SetPrototype%": ["Set", "prototype"],
      "%SharedArrayBufferPrototype%": ["SharedArrayBuffer", "prototype"],
      "%StringPrototype%": ["String", "prototype"],
      "%SymbolPrototype%": ["Symbol", "prototype"],
      "%SyntaxErrorPrototype%": ["SyntaxError", "prototype"],
      "%TypedArrayPrototype%": ["TypedArray", "prototype"],
      "%TypeErrorPrototype%": ["TypeError", "prototype"],
      "%Uint8ArrayPrototype%": ["Uint8Array", "prototype"],
      "%Uint8ClampedArrayPrototype%": ["Uint8ClampedArray", "prototype"],
      "%Uint16ArrayPrototype%": ["Uint16Array", "prototype"],
      "%Uint32ArrayPrototype%": ["Uint32Array", "prototype"],
      "%URIErrorPrototype%": ["URIError", "prototype"],
      "%WeakMapPrototype%": ["WeakMap", "prototype"],
      "%WeakSetPrototype%": ["WeakSet", "prototype"]
    };
    var bind2 = require_function_bind();
    var hasOwn = require_hasown();
    var $concat = bind2.call($call, Array.prototype.concat);
    var $spliceApply = bind2.call($apply, Array.prototype.splice);
    var $replace = bind2.call($call, String.prototype.replace);
    var $strSlice = bind2.call($call, String.prototype.slice);
    var $exec = bind2.call($call, RegExp.prototype.exec);
    var rePropName = /[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g;
    var reEscapeChar = /\\(\\)?/g;
    var stringToPath = function stringToPath2(string) {
      var first = $strSlice(string, 0, 1);
      var last3 = $strSlice(string, -1);
      if (first === "%" && last3 !== "%") {
        throw new $SyntaxError("invalid intrinsic syntax, expected closing `%`");
      } else if (last3 === "%" && first !== "%") {
        throw new $SyntaxError("invalid intrinsic syntax, expected opening `%`");
      }
      var result2 = [];
      $replace(string, rePropName, function(match, number, quote, subString) {
        result2[result2.length] = quote ? $replace(subString, reEscapeChar, "$1") : number || match;
      });
      return result2;
    };
    var getBaseIntrinsic = function getBaseIntrinsic2(name, allowMissing) {
      var intrinsicName = name;
      var alias;
      if (hasOwn(LEGACY_ALIASES, intrinsicName)) {
        alias = LEGACY_ALIASES[intrinsicName];
        intrinsicName = "%" + alias[0] + "%";
      }
      if (hasOwn(INTRINSICS, intrinsicName)) {
        var value = INTRINSICS[intrinsicName];
        if (value === needsEval) {
          value = doEval(intrinsicName);
        }
        if (typeof value === "undefined" && !allowMissing) {
          throw new $TypeError("intrinsic " + name + " exists, but is not available. Please file an issue!");
        }
        return {
          alias,
          name: intrinsicName,
          value
        };
      }
      throw new $SyntaxError("intrinsic " + name + " does not exist!");
    };
    module.exports = function GetIntrinsic(name, allowMissing) {
      if (typeof name !== "string" || name.length === 0) {
        throw new $TypeError("intrinsic name must be a non-empty string");
      }
      if (arguments.length > 1 && typeof allowMissing !== "boolean") {
        throw new $TypeError('"allowMissing" argument must be a boolean');
      }
      if ($exec(/^%?[^%]*%?$/, name) === null) {
        throw new $SyntaxError("`%` may not be present anywhere but at the beginning and end of the intrinsic name");
      }
      var parts = stringToPath(name);
      var intrinsicBaseName = parts.length > 0 ? parts[0] : "";
      var intrinsic = getBaseIntrinsic("%" + intrinsicBaseName + "%", allowMissing);
      var intrinsicRealName = intrinsic.name;
      var value = intrinsic.value;
      var skipFurtherCaching = false;
      var alias = intrinsic.alias;
      if (alias) {
        intrinsicBaseName = alias[0];
        $spliceApply(parts, $concat([0, 1], alias));
      }
      for (var i = 1, isOwn = true; i < parts.length; i += 1) {
        var part = parts[i];
        var first = $strSlice(part, 0, 1);
        var last3 = $strSlice(part, -1);
        if ((first === '"' || first === "'" || first === "`" || (last3 === '"' || last3 === "'" || last3 === "`")) && first !== last3) {
          throw new $SyntaxError("property names with quotes must have matching quotes");
        }
        if (part === "constructor" || !isOwn) {
          skipFurtherCaching = true;
        }
        intrinsicBaseName += "." + part;
        intrinsicRealName = "%" + intrinsicBaseName + "%";
        if (hasOwn(INTRINSICS, intrinsicRealName)) {
          value = INTRINSICS[intrinsicRealName];
        } else if (value != null) {
          if (!(part in value)) {
            if (!allowMissing) {
              throw new $TypeError("base intrinsic for " + name + " exists, but the property is not available.");
            }
            return void 0;
          }
          if ($gOPD && i + 1 >= parts.length) {
            var desc = $gOPD(value, part);
            isOwn = !!desc;
            if (isOwn && "get" in desc && !("originalValue" in desc.get)) {
              value = desc.get;
            } else {
              value = value[part];
            }
          } else {
            isOwn = hasOwn(value, part);
            value = value[part];
          }
          if (isOwn && !skipFurtherCaching) {
            INTRINSICS[intrinsicRealName] = value;
          }
        }
      }
      return value;
    };
  }
});

// node_modules/call-bound/index.js
var require_call_bound = __commonJS({
  "node_modules/call-bound/index.js"(exports, module) {
    "use strict";
    var GetIntrinsic = require_get_intrinsic();
    var callBindBasic = require_call_bind_apply_helpers();
    var $indexOf = callBindBasic([GetIntrinsic("%String.prototype.indexOf%")]);
    module.exports = function callBoundIntrinsic(name, allowMissing) {
      var intrinsic = (
        /** @type {(this: unknown, ...args: unknown[]) => unknown} */
        GetIntrinsic(name, !!allowMissing)
      );
      if (typeof intrinsic === "function" && $indexOf(name, ".prototype.") > -1) {
        return callBindBasic(
          /** @type {const} */
          [intrinsic]
        );
      }
      return intrinsic;
    };
  }
});

// node_modules/side-channel-map/index.js
var require_side_channel_map = __commonJS({
  "node_modules/side-channel-map/index.js"(exports, module) {
    "use strict";
    var GetIntrinsic = require_get_intrinsic();
    var callBound = require_call_bound();
    var inspect = require_object_inspect();
    var $TypeError = require_type();
    var $Map = GetIntrinsic("%Map%", true);
    var $mapGet = callBound("Map.prototype.get", true);
    var $mapSet = callBound("Map.prototype.set", true);
    var $mapHas = callBound("Map.prototype.has", true);
    var $mapDelete = callBound("Map.prototype.delete", true);
    var $mapSize = callBound("Map.prototype.size", true);
    module.exports = !!$Map && /** @type {Exclude<import('.'), false>} */
    function getSideChannelMap() {
      var $m;
      var channel = {
        assert: function(key) {
          if (!channel.has(key)) {
            throw new $TypeError("Side channel does not contain " + inspect(key));
          }
        },
        "delete": function(key) {
          if ($m) {
            var result2 = $mapDelete($m, key);
            if ($mapSize($m) === 0) {
              $m = void 0;
            }
            return result2;
          }
          return false;
        },
        get: function(key) {
          if ($m) {
            return $mapGet($m, key);
          }
        },
        has: function(key) {
          if ($m) {
            return $mapHas($m, key);
          }
          return false;
        },
        set: function(key, value) {
          if (!$m) {
            $m = new $Map();
          }
          $mapSet($m, key, value);
        }
      };
      return channel;
    };
  }
});

// node_modules/side-channel-weakmap/index.js
var require_side_channel_weakmap = __commonJS({
  "node_modules/side-channel-weakmap/index.js"(exports, module) {
    "use strict";
    var GetIntrinsic = require_get_intrinsic();
    var callBound = require_call_bound();
    var inspect = require_object_inspect();
    var getSideChannelMap = require_side_channel_map();
    var $TypeError = require_type();
    var $WeakMap = GetIntrinsic("%WeakMap%", true);
    var $weakMapGet = callBound("WeakMap.prototype.get", true);
    var $weakMapSet = callBound("WeakMap.prototype.set", true);
    var $weakMapHas = callBound("WeakMap.prototype.has", true);
    var $weakMapDelete = callBound("WeakMap.prototype.delete", true);
    module.exports = $WeakMap ? (
      /** @type {Exclude<import('.'), false>} */
      function getSideChannelWeakMap() {
        var $wm;
        var $m;
        var channel = {
          assert: function(key) {
            if (!channel.has(key)) {
              throw new $TypeError("Side channel does not contain " + inspect(key));
            }
          },
          "delete": function(key) {
            if ($WeakMap && key && (typeof key === "object" || typeof key === "function")) {
              if ($wm) {
                return $weakMapDelete($wm, key);
              }
            } else if (getSideChannelMap) {
              if ($m) {
                return $m["delete"](key);
              }
            }
            return false;
          },
          get: function(key) {
            if ($WeakMap && key && (typeof key === "object" || typeof key === "function")) {
              if ($wm) {
                return $weakMapGet($wm, key);
              }
            }
            return $m && $m.get(key);
          },
          has: function(key) {
            if ($WeakMap && key && (typeof key === "object" || typeof key === "function")) {
              if ($wm) {
                return $weakMapHas($wm, key);
              }
            }
            return !!$m && $m.has(key);
          },
          set: function(key, value) {
            if ($WeakMap && key && (typeof key === "object" || typeof key === "function")) {
              if (!$wm) {
                $wm = new $WeakMap();
              }
              $weakMapSet($wm, key, value);
            } else if (getSideChannelMap) {
              if (!$m) {
                $m = getSideChannelMap();
              }
              $m.set(key, value);
            }
          }
        };
        return channel;
      }
    ) : getSideChannelMap;
  }
});

// node_modules/side-channel/index.js
var require_side_channel = __commonJS({
  "node_modules/side-channel/index.js"(exports, module) {
    "use strict";
    var $TypeError = require_type();
    var inspect = require_object_inspect();
    var getSideChannelList = require_side_channel_list();
    var getSideChannelMap = require_side_channel_map();
    var getSideChannelWeakMap = require_side_channel_weakmap();
    var makeChannel = getSideChannelWeakMap || getSideChannelMap || getSideChannelList;
    module.exports = function getSideChannel() {
      var $channelData;
      var channel = {
        assert: function(key) {
          if (!channel.has(key)) {
            throw new $TypeError("Side channel does not contain " + inspect(key));
          }
        },
        "delete": function(key) {
          return !!$channelData && $channelData["delete"](key);
        },
        get: function(key) {
          return $channelData && $channelData.get(key);
        },
        has: function(key) {
          return !!$channelData && $channelData.has(key);
        },
        set: function(key, value) {
          if (!$channelData) {
            $channelData = makeChannel();
          }
          $channelData.set(key, value);
        }
      };
      return channel;
    };
  }
});

// node_modules/qs/lib/formats.js
var require_formats = __commonJS({
  "node_modules/qs/lib/formats.js"(exports, module) {
    "use strict";
    var replace2 = String.prototype.replace;
    var percentTwenties = /%20/g;
    var Format = {
      RFC1738: "RFC1738",
      RFC3986: "RFC3986"
    };
    module.exports = {
      "default": Format.RFC3986,
      formatters: {
        RFC1738: function(value) {
          return replace2.call(value, percentTwenties, "+");
        },
        RFC3986: function(value) {
          return String(value);
        }
      },
      RFC1738: Format.RFC1738,
      RFC3986: Format.RFC3986
    };
  }
});

// node_modules/qs/lib/utils.js
var require_utils = __commonJS({
  "node_modules/qs/lib/utils.js"(exports, module) {
    "use strict";
    var formats = require_formats();
    var has2 = Object.prototype.hasOwnProperty;
    var isArray2 = Array.isArray;
    var hexTable = function() {
      var array = [];
      for (var i = 0; i < 256; ++i) {
        array.push("%" + ((i < 16 ? "0" : "") + i.toString(16)).toUpperCase());
      }
      return array;
    }();
    var compactQueue = function compactQueue2(queue) {
      while (queue.length > 1) {
        var item = queue.pop();
        var obj = item.obj[item.prop];
        if (isArray2(obj)) {
          var compacted = [];
          for (var j2 = 0; j2 < obj.length; ++j2) {
            if (typeof obj[j2] !== "undefined") {
              compacted.push(obj[j2]);
            }
          }
          item.obj[item.prop] = compacted;
        }
      }
    };
    var arrayToObject = function arrayToObject2(source, options) {
      var obj = options && options.plainObjects ? { __proto__: null } : {};
      for (var i = 0; i < source.length; ++i) {
        if (typeof source[i] !== "undefined") {
          obj[i] = source[i];
        }
      }
      return obj;
    };
    var merge3 = function merge4(target, source, options) {
      if (!source) {
        return target;
      }
      if (typeof source !== "object" && typeof source !== "function") {
        if (isArray2(target)) {
          target.push(source);
        } else if (target && typeof target === "object") {
          if (options && (options.plainObjects || options.allowPrototypes) || !has2.call(Object.prototype, source)) {
            target[source] = true;
          }
        } else {
          return [target, source];
        }
        return target;
      }
      if (!target || typeof target !== "object") {
        return [target].concat(source);
      }
      var mergeTarget = target;
      if (isArray2(target) && !isArray2(source)) {
        mergeTarget = arrayToObject(target, options);
      }
      if (isArray2(target) && isArray2(source)) {
        source.forEach(function(item, i) {
          if (has2.call(target, i)) {
            var targetItem = target[i];
            if (targetItem && typeof targetItem === "object" && item && typeof item === "object") {
              target[i] = merge4(targetItem, item, options);
            } else {
              target.push(item);
            }
          } else {
            target[i] = item;
          }
        });
        return target;
      }
      return Object.keys(source).reduce(function(acc, key) {
        var value = source[key];
        if (has2.call(acc, key)) {
          acc[key] = merge4(acc[key], value, options);
        } else {
          acc[key] = value;
        }
        return acc;
      }, mergeTarget);
    };
    var assign2 = function assignSingleSource(target, source) {
      return Object.keys(source).reduce(function(acc, key) {
        acc[key] = source[key];
        return acc;
      }, target);
    };
    var decode = function(str, defaultDecoder, charset) {
      var strWithoutPlus = str.replace(/\+/g, " ");
      if (charset === "iso-8859-1") {
        return strWithoutPlus.replace(/%[0-9a-f]{2}/gi, unescape);
      }
      try {
        return decodeURIComponent(strWithoutPlus);
      } catch (e) {
        return strWithoutPlus;
      }
    };
    var limit = 1024;
    var encode = function encode2(str, defaultEncoder, charset, kind, format) {
      if (str.length === 0) {
        return str;
      }
      var string = str;
      if (typeof str === "symbol") {
        string = Symbol.prototype.toString.call(str);
      } else if (typeof str !== "string") {
        string = String(str);
      }
      if (charset === "iso-8859-1") {
        return escape(string).replace(/%u[0-9a-f]{4}/gi, function($0) {
          return "%26%23" + parseInt($0.slice(2), 16) + "%3B";
        });
      }
      var out = "";
      for (var j2 = 0; j2 < string.length; j2 += limit) {
        var segment = string.length >= limit ? string.slice(j2, j2 + limit) : string;
        var arr = [];
        for (var i = 0; i < segment.length; ++i) {
          var c = segment.charCodeAt(i);
          if (c === 45 || c === 46 || c === 95 || c === 126 || c >= 48 && c <= 57 || c >= 65 && c <= 90 || c >= 97 && c <= 122 || format === formats.RFC1738 && (c === 40 || c === 41)) {
            arr[arr.length] = segment.charAt(i);
            continue;
          }
          if (c < 128) {
            arr[arr.length] = hexTable[c];
            continue;
          }
          if (c < 2048) {
            arr[arr.length] = hexTable[192 | c >> 6] + hexTable[128 | c & 63];
            continue;
          }
          if (c < 55296 || c >= 57344) {
            arr[arr.length] = hexTable[224 | c >> 12] + hexTable[128 | c >> 6 & 63] + hexTable[128 | c & 63];
            continue;
          }
          i += 1;
          c = 65536 + ((c & 1023) << 10 | segment.charCodeAt(i) & 1023);
          arr[arr.length] = hexTable[240 | c >> 18] + hexTable[128 | c >> 12 & 63] + hexTable[128 | c >> 6 & 63] + hexTable[128 | c & 63];
        }
        out += arr.join("");
      }
      return out;
    };
    var compact3 = function compact4(value) {
      var queue = [{ obj: { o: value }, prop: "o" }];
      var refs = [];
      for (var i = 0; i < queue.length; ++i) {
        var item = queue[i];
        var obj = item.obj[item.prop];
        var keys2 = Object.keys(obj);
        for (var j2 = 0; j2 < keys2.length; ++j2) {
          var key = keys2[j2];
          var val = obj[key];
          if (typeof val === "object" && val !== null && refs.indexOf(val) === -1) {
            queue.push({ obj, prop: key });
            refs.push(val);
          }
        }
      }
      compactQueue(queue);
      return value;
    };
    var isRegExp3 = function isRegExp4(obj) {
      return Object.prototype.toString.call(obj) === "[object RegExp]";
    };
    var isBuffer3 = function isBuffer4(obj) {
      if (!obj || typeof obj !== "object") {
        return false;
      }
      return !!(obj.constructor && obj.constructor.isBuffer && obj.constructor.isBuffer(obj));
    };
    var combine = function combine2(a2, b2) {
      return [].concat(a2, b2);
    };
    var maybeMap = function maybeMap2(val, fn) {
      if (isArray2(val)) {
        var mapped = [];
        for (var i = 0; i < val.length; i += 1) {
          mapped.push(fn(val[i]));
        }
        return mapped;
      }
      return fn(val);
    };
    module.exports = {
      arrayToObject,
      assign: assign2,
      combine,
      compact: compact3,
      decode,
      encode,
      isBuffer: isBuffer3,
      isRegExp: isRegExp3,
      maybeMap,
      merge: merge3
    };
  }
});

// node_modules/qs/lib/stringify.js
var require_stringify = __commonJS({
  "node_modules/qs/lib/stringify.js"(exports, module) {
    "use strict";
    var getSideChannel = require_side_channel();
    var utils = require_utils();
    var formats = require_formats();
    var has2 = Object.prototype.hasOwnProperty;
    var arrayPrefixGenerators = {
      brackets: function brackets(prefix) {
        return prefix + "[]";
      },
      comma: "comma",
      indices: function indices(prefix, key) {
        return prefix + "[" + key + "]";
      },
      repeat: function repeat2(prefix) {
        return prefix;
      }
    };
    var isArray2 = Array.isArray;
    var push = Array.prototype.push;
    var pushToArray = function(arr, valueOrArray) {
      push.apply(arr, isArray2(valueOrArray) ? valueOrArray : [valueOrArray]);
    };
    var toISO = Date.prototype.toISOString;
    var defaultFormat = formats["default"];
    var defaults2 = {
      addQueryPrefix: false,
      allowDots: false,
      allowEmptyArrays: false,
      arrayFormat: "indices",
      charset: "utf-8",
      charsetSentinel: false,
      commaRoundTrip: false,
      delimiter: "&",
      encode: true,
      encodeDotInKeys: false,
      encoder: utils.encode,
      encodeValuesOnly: false,
      filter: void 0,
      format: defaultFormat,
      formatter: formats.formatters[defaultFormat],
      // deprecated
      indices: false,
      serializeDate: function serializeDate(date) {
        return toISO.call(date);
      },
      skipNulls: false,
      strictNullHandling: false
    };
    var isNonNullishPrimitive = function isNonNullishPrimitive2(v2) {
      return typeof v2 === "string" || typeof v2 === "number" || typeof v2 === "boolean" || typeof v2 === "symbol" || typeof v2 === "bigint";
    };
    var sentinel = {};
    var stringify2 = function stringify3(object, prefix, generateArrayPrefix, commaRoundTrip, allowEmptyArrays, strictNullHandling, skipNulls, encodeDotInKeys, encoder, filter2, sort, allowDots, serializeDate, format, formatter, encodeValuesOnly, charset, sideChannel) {
      var obj = object;
      var tmpSc = sideChannel;
      var step = 0;
      var findFlag = false;
      while ((tmpSc = tmpSc.get(sentinel)) !== void 0 && !findFlag) {
        var pos = tmpSc.get(object);
        step += 1;
        if (typeof pos !== "undefined") {
          if (pos === step) {
            throw new RangeError("Cyclic object value");
          } else {
            findFlag = true;
          }
        }
        if (typeof tmpSc.get(sentinel) === "undefined") {
          step = 0;
        }
      }
      if (typeof filter2 === "function") {
        obj = filter2(prefix, obj);
      } else if (obj instanceof Date) {
        obj = serializeDate(obj);
      } else if (generateArrayPrefix === "comma" && isArray2(obj)) {
        obj = utils.maybeMap(obj, function(value2) {
          if (value2 instanceof Date) {
            return serializeDate(value2);
          }
          return value2;
        });
      }
      if (obj === null) {
        if (strictNullHandling) {
          return encoder && !encodeValuesOnly ? encoder(prefix, defaults2.encoder, charset, "key", format) : prefix;
        }
        obj = "";
      }
      if (isNonNullishPrimitive(obj) || utils.isBuffer(obj)) {
        if (encoder) {
          var keyValue = encodeValuesOnly ? prefix : encoder(prefix, defaults2.encoder, charset, "key", format);
          return [formatter(keyValue) + "=" + formatter(encoder(obj, defaults2.encoder, charset, "value", format))];
        }
        return [formatter(prefix) + "=" + formatter(String(obj))];
      }
      var values2 = [];
      if (typeof obj === "undefined") {
        return values2;
      }
      var objKeys;
      if (generateArrayPrefix === "comma" && isArray2(obj)) {
        if (encodeValuesOnly && encoder) {
          obj = utils.maybeMap(obj, encoder);
        }
        objKeys = [{ value: obj.length > 0 ? obj.join(",") || null : void 0 }];
      } else if (isArray2(filter2)) {
        objKeys = filter2;
      } else {
        var keys2 = Object.keys(obj);
        objKeys = sort ? keys2.sort(sort) : keys2;
      }
      var encodedPrefix = encodeDotInKeys ? String(prefix).replace(/\./g, "%2E") : String(prefix);
      var adjustedPrefix = commaRoundTrip && isArray2(obj) && obj.length === 1 ? encodedPrefix + "[]" : encodedPrefix;
      if (allowEmptyArrays && isArray2(obj) && obj.length === 0) {
        return adjustedPrefix + "[]";
      }
      for (var j2 = 0; j2 < objKeys.length; ++j2) {
        var key = objKeys[j2];
        var value = typeof key === "object" && key && typeof key.value !== "undefined" ? key.value : obj[key];
        if (skipNulls && value === null) {
          continue;
        }
        var encodedKey = allowDots && encodeDotInKeys ? String(key).replace(/\./g, "%2E") : String(key);
        var keyPrefix = isArray2(obj) ? typeof generateArrayPrefix === "function" ? generateArrayPrefix(adjustedPrefix, encodedKey) : adjustedPrefix : adjustedPrefix + (allowDots ? "." + encodedKey : "[" + encodedKey + "]");
        sideChannel.set(object, step);
        var valueSideChannel = getSideChannel();
        valueSideChannel.set(sentinel, sideChannel);
        pushToArray(values2, stringify3(
          value,
          keyPrefix,
          generateArrayPrefix,
          commaRoundTrip,
          allowEmptyArrays,
          strictNullHandling,
          skipNulls,
          encodeDotInKeys,
          generateArrayPrefix === "comma" && encodeValuesOnly && isArray2(obj) ? null : encoder,
          filter2,
          sort,
          allowDots,
          serializeDate,
          format,
          formatter,
          encodeValuesOnly,
          charset,
          valueSideChannel
        ));
      }
      return values2;
    };
    var normalizeStringifyOptions = function normalizeStringifyOptions2(opts) {
      if (!opts) {
        return defaults2;
      }
      if (typeof opts.allowEmptyArrays !== "undefined" && typeof opts.allowEmptyArrays !== "boolean") {
        throw new TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");
      }
      if (typeof opts.encodeDotInKeys !== "undefined" && typeof opts.encodeDotInKeys !== "boolean") {
        throw new TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");
      }
      if (opts.encoder !== null && typeof opts.encoder !== "undefined" && typeof opts.encoder !== "function") {
        throw new TypeError("Encoder has to be a function.");
      }
      var charset = opts.charset || defaults2.charset;
      if (typeof opts.charset !== "undefined" && opts.charset !== "utf-8" && opts.charset !== "iso-8859-1") {
        throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");
      }
      var format = formats["default"];
      if (typeof opts.format !== "undefined") {
        if (!has2.call(formats.formatters, opts.format)) {
          throw new TypeError("Unknown format option provided.");
        }
        format = opts.format;
      }
      var formatter = formats.formatters[format];
      var filter2 = defaults2.filter;
      if (typeof opts.filter === "function" || isArray2(opts.filter)) {
        filter2 = opts.filter;
      }
      var arrayFormat;
      if (opts.arrayFormat in arrayPrefixGenerators) {
        arrayFormat = opts.arrayFormat;
      } else if ("indices" in opts) {
        arrayFormat = opts.indices ? "indices" : "repeat";
      } else {
        arrayFormat = defaults2.arrayFormat;
      }
      if ("commaRoundTrip" in opts && typeof opts.commaRoundTrip !== "boolean") {
        throw new TypeError("`commaRoundTrip` must be a boolean, or absent");
      }
      var allowDots = typeof opts.allowDots === "undefined" ? opts.encodeDotInKeys === true ? true : defaults2.allowDots : !!opts.allowDots;
      return {
        addQueryPrefix: typeof opts.addQueryPrefix === "boolean" ? opts.addQueryPrefix : defaults2.addQueryPrefix,
        allowDots,
        allowEmptyArrays: typeof opts.allowEmptyArrays === "boolean" ? !!opts.allowEmptyArrays : defaults2.allowEmptyArrays,
        arrayFormat,
        charset,
        charsetSentinel: typeof opts.charsetSentinel === "boolean" ? opts.charsetSentinel : defaults2.charsetSentinel,
        commaRoundTrip: !!opts.commaRoundTrip,
        delimiter: typeof opts.delimiter === "undefined" ? defaults2.delimiter : opts.delimiter,
        encode: typeof opts.encode === "boolean" ? opts.encode : defaults2.encode,
        encodeDotInKeys: typeof opts.encodeDotInKeys === "boolean" ? opts.encodeDotInKeys : defaults2.encodeDotInKeys,
        encoder: typeof opts.encoder === "function" ? opts.encoder : defaults2.encoder,
        encodeValuesOnly: typeof opts.encodeValuesOnly === "boolean" ? opts.encodeValuesOnly : defaults2.encodeValuesOnly,
        filter: filter2,
        format,
        formatter,
        serializeDate: typeof opts.serializeDate === "function" ? opts.serializeDate : defaults2.serializeDate,
        skipNulls: typeof opts.skipNulls === "boolean" ? opts.skipNulls : defaults2.skipNulls,
        sort: typeof opts.sort === "function" ? opts.sort : null,
        strictNullHandling: typeof opts.strictNullHandling === "boolean" ? opts.strictNullHandling : defaults2.strictNullHandling
      };
    };
    module.exports = function(object, opts) {
      var obj = object;
      var options = normalizeStringifyOptions(opts);
      var objKeys;
      var filter2;
      if (typeof options.filter === "function") {
        filter2 = options.filter;
        obj = filter2("", obj);
      } else if (isArray2(options.filter)) {
        filter2 = options.filter;
        objKeys = filter2;
      }
      var keys2 = [];
      if (typeof obj !== "object" || obj === null) {
        return "";
      }
      var generateArrayPrefix = arrayPrefixGenerators[options.arrayFormat];
      var commaRoundTrip = generateArrayPrefix === "comma" && options.commaRoundTrip;
      if (!objKeys) {
        objKeys = Object.keys(obj);
      }
      if (options.sort) {
        objKeys.sort(options.sort);
      }
      var sideChannel = getSideChannel();
      for (var i = 0; i < objKeys.length; ++i) {
        var key = objKeys[i];
        var value = obj[key];
        if (options.skipNulls && value === null) {
          continue;
        }
        pushToArray(keys2, stringify2(
          value,
          key,
          generateArrayPrefix,
          commaRoundTrip,
          options.allowEmptyArrays,
          options.strictNullHandling,
          options.skipNulls,
          options.encodeDotInKeys,
          options.encode ? options.encoder : null,
          options.filter,
          options.sort,
          options.allowDots,
          options.serializeDate,
          options.format,
          options.formatter,
          options.encodeValuesOnly,
          options.charset,
          sideChannel
        ));
      }
      var joined = keys2.join(options.delimiter);
      var prefix = options.addQueryPrefix === true ? "?" : "";
      if (options.charsetSentinel) {
        if (options.charset === "iso-8859-1") {
          prefix += "utf8=%26%2310003%3B&";
        } else {
          prefix += "utf8=%E2%9C%93&";
        }
      }
      return joined.length > 0 ? prefix + joined : "";
    };
  }
});

// node_modules/qs/lib/parse.js
var require_parse = __commonJS({
  "node_modules/qs/lib/parse.js"(exports, module) {
    "use strict";
    var utils = require_utils();
    var has2 = Object.prototype.hasOwnProperty;
    var isArray2 = Array.isArray;
    var defaults2 = {
      allowDots: false,
      allowEmptyArrays: false,
      allowPrototypes: false,
      allowSparse: false,
      arrayLimit: 20,
      charset: "utf-8",
      charsetSentinel: false,
      comma: false,
      decodeDotInKeys: false,
      decoder: utils.decode,
      delimiter: "&",
      depth: 5,
      duplicates: "combine",
      ignoreQueryPrefix: false,
      interpretNumericEntities: false,
      parameterLimit: 1e3,
      parseArrays: true,
      plainObjects: false,
      strictDepth: false,
      strictNullHandling: false,
      throwOnLimitExceeded: false
    };
    var interpretNumericEntities = function(str) {
      return str.replace(/&#(\d+);/g, function($0, numberStr) {
        return String.fromCharCode(parseInt(numberStr, 10));
      });
    };
    var parseArrayValue = function(val, options, currentArrayLength) {
      if (val && typeof val === "string" && options.comma && val.indexOf(",") > -1) {
        return val.split(",");
      }
      if (options.throwOnLimitExceeded && currentArrayLength >= options.arrayLimit) {
        throw new RangeError("Array limit exceeded. Only " + options.arrayLimit + " element" + (options.arrayLimit === 1 ? "" : "s") + " allowed in an array.");
      }
      return val;
    };
    var isoSentinel = "utf8=%26%2310003%3B";
    var charsetSentinel = "utf8=%E2%9C%93";
    var parseValues = function parseQueryStringValues(str, options) {
      var obj = { __proto__: null };
      var cleanStr = options.ignoreQueryPrefix ? str.replace(/^\?/, "") : str;
      cleanStr = cleanStr.replace(/%5B/gi, "[").replace(/%5D/gi, "]");
      var limit = options.parameterLimit === Infinity ? void 0 : options.parameterLimit;
      var parts = cleanStr.split(
        options.delimiter,
        options.throwOnLimitExceeded ? limit + 1 : limit
      );
      if (options.throwOnLimitExceeded && parts.length > limit) {
        throw new RangeError("Parameter limit exceeded. Only " + limit + " parameter" + (limit === 1 ? "" : "s") + " allowed.");
      }
      var skipIndex = -1;
      var i;
      var charset = options.charset;
      if (options.charsetSentinel) {
        for (i = 0; i < parts.length; ++i) {
          if (parts[i].indexOf("utf8=") === 0) {
            if (parts[i] === charsetSentinel) {
              charset = "utf-8";
            } else if (parts[i] === isoSentinel) {
              charset = "iso-8859-1";
            }
            skipIndex = i;
            i = parts.length;
          }
        }
      }
      for (i = 0; i < parts.length; ++i) {
        if (i === skipIndex) {
          continue;
        }
        var part = parts[i];
        var bracketEqualsPos = part.indexOf("]=");
        var pos = bracketEqualsPos === -1 ? part.indexOf("=") : bracketEqualsPos + 1;
        var key;
        var val;
        if (pos === -1) {
          key = options.decoder(part, defaults2.decoder, charset, "key");
          val = options.strictNullHandling ? null : "";
        } else {
          key = options.decoder(part.slice(0, pos), defaults2.decoder, charset, "key");
          val = utils.maybeMap(
            parseArrayValue(
              part.slice(pos + 1),
              options,
              isArray2(obj[key]) ? obj[key].length : 0
            ),
            function(encodedVal) {
              return options.decoder(encodedVal, defaults2.decoder, charset, "value");
            }
          );
        }
        if (val && options.interpretNumericEntities && charset === "iso-8859-1") {
          val = interpretNumericEntities(String(val));
        }
        if (part.indexOf("[]=") > -1) {
          val = isArray2(val) ? [val] : val;
        }
        var existing = has2.call(obj, key);
        if (existing && options.duplicates === "combine") {
          obj[key] = utils.combine(obj[key], val);
        } else if (!existing || options.duplicates === "last") {
          obj[key] = val;
        }
      }
      return obj;
    };
    var parseObject = function(chain, val, options, valuesParsed) {
      var currentArrayLength = 0;
      if (chain.length > 0 && chain[chain.length - 1] === "[]") {
        var parentKey = chain.slice(0, -1).join("");
        currentArrayLength = Array.isArray(val) && val[parentKey] ? val[parentKey].length : 0;
      }
      var leaf = valuesParsed ? val : parseArrayValue(val, options, currentArrayLength);
      for (var i = chain.length - 1; i >= 0; --i) {
        var obj;
        var root = chain[i];
        if (root === "[]" && options.parseArrays) {
          obj = options.allowEmptyArrays && (leaf === "" || options.strictNullHandling && leaf === null) ? [] : utils.combine([], leaf);
        } else {
          obj = options.plainObjects ? { __proto__: null } : {};
          var cleanRoot = root.charAt(0) === "[" && root.charAt(root.length - 1) === "]" ? root.slice(1, -1) : root;
          var decodedRoot = options.decodeDotInKeys ? cleanRoot.replace(/%2E/g, ".") : cleanRoot;
          var index = parseInt(decodedRoot, 10);
          if (!options.parseArrays && decodedRoot === "") {
            obj = { 0: leaf };
          } else if (!isNaN(index) && root !== decodedRoot && String(index) === decodedRoot && index >= 0 && (options.parseArrays && index <= options.arrayLimit)) {
            obj = [];
            obj[index] = leaf;
          } else if (decodedRoot !== "__proto__") {
            obj[decodedRoot] = leaf;
          }
        }
        leaf = obj;
      }
      return leaf;
    };
    var parseKeys = function parseQueryStringKeys(givenKey, val, options, valuesParsed) {
      if (!givenKey) {
        return;
      }
      var key = options.allowDots ? givenKey.replace(/\.([^.[]+)/g, "[$1]") : givenKey;
      var brackets = /(\[[^[\]]*])/;
      var child = /(\[[^[\]]*])/g;
      var segment = options.depth > 0 && brackets.exec(key);
      var parent = segment ? key.slice(0, segment.index) : key;
      var keys2 = [];
      if (parent) {
        if (!options.plainObjects && has2.call(Object.prototype, parent)) {
          if (!options.allowPrototypes) {
            return;
          }
        }
        keys2.push(parent);
      }
      var i = 0;
      while (options.depth > 0 && (segment = child.exec(key)) !== null && i < options.depth) {
        i += 1;
        if (!options.plainObjects && has2.call(Object.prototype, segment[1].slice(1, -1))) {
          if (!options.allowPrototypes) {
            return;
          }
        }
        keys2.push(segment[1]);
      }
      if (segment) {
        if (options.strictDepth === true) {
          throw new RangeError("Input depth exceeded depth option of " + options.depth + " and strictDepth is true");
        }
        keys2.push("[" + key.slice(segment.index) + "]");
      }
      return parseObject(keys2, val, options, valuesParsed);
    };
    var normalizeParseOptions = function normalizeParseOptions2(opts) {
      if (!opts) {
        return defaults2;
      }
      if (typeof opts.allowEmptyArrays !== "undefined" && typeof opts.allowEmptyArrays !== "boolean") {
        throw new TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");
      }
      if (typeof opts.decodeDotInKeys !== "undefined" && typeof opts.decodeDotInKeys !== "boolean") {
        throw new TypeError("`decodeDotInKeys` option can only be `true` or `false`, when provided");
      }
      if (opts.decoder !== null && typeof opts.decoder !== "undefined" && typeof opts.decoder !== "function") {
        throw new TypeError("Decoder has to be a function.");
      }
      if (typeof opts.charset !== "undefined" && opts.charset !== "utf-8" && opts.charset !== "iso-8859-1") {
        throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");
      }
      if (typeof opts.throwOnLimitExceeded !== "undefined" && typeof opts.throwOnLimitExceeded !== "boolean") {
        throw new TypeError("`throwOnLimitExceeded` option must be a boolean");
      }
      var charset = typeof opts.charset === "undefined" ? defaults2.charset : opts.charset;
      var duplicates = typeof opts.duplicates === "undefined" ? defaults2.duplicates : opts.duplicates;
      if (duplicates !== "combine" && duplicates !== "first" && duplicates !== "last") {
        throw new TypeError("The duplicates option must be either combine, first, or last");
      }
      var allowDots = typeof opts.allowDots === "undefined" ? opts.decodeDotInKeys === true ? true : defaults2.allowDots : !!opts.allowDots;
      return {
        allowDots,
        allowEmptyArrays: typeof opts.allowEmptyArrays === "boolean" ? !!opts.allowEmptyArrays : defaults2.allowEmptyArrays,
        allowPrototypes: typeof opts.allowPrototypes === "boolean" ? opts.allowPrototypes : defaults2.allowPrototypes,
        allowSparse: typeof opts.allowSparse === "boolean" ? opts.allowSparse : defaults2.allowSparse,
        arrayLimit: typeof opts.arrayLimit === "number" ? opts.arrayLimit : defaults2.arrayLimit,
        charset,
        charsetSentinel: typeof opts.charsetSentinel === "boolean" ? opts.charsetSentinel : defaults2.charsetSentinel,
        comma: typeof opts.comma === "boolean" ? opts.comma : defaults2.comma,
        decodeDotInKeys: typeof opts.decodeDotInKeys === "boolean" ? opts.decodeDotInKeys : defaults2.decodeDotInKeys,
        decoder: typeof opts.decoder === "function" ? opts.decoder : defaults2.decoder,
        delimiter: typeof opts.delimiter === "string" || utils.isRegExp(opts.delimiter) ? opts.delimiter : defaults2.delimiter,
        // eslint-disable-next-line no-implicit-coercion, no-extra-parens
        depth: typeof opts.depth === "number" || opts.depth === false ? +opts.depth : defaults2.depth,
        duplicates,
        ignoreQueryPrefix: opts.ignoreQueryPrefix === true,
        interpretNumericEntities: typeof opts.interpretNumericEntities === "boolean" ? opts.interpretNumericEntities : defaults2.interpretNumericEntities,
        parameterLimit: typeof opts.parameterLimit === "number" ? opts.parameterLimit : defaults2.parameterLimit,
        parseArrays: opts.parseArrays !== false,
        plainObjects: typeof opts.plainObjects === "boolean" ? opts.plainObjects : defaults2.plainObjects,
        strictDepth: typeof opts.strictDepth === "boolean" ? !!opts.strictDepth : defaults2.strictDepth,
        strictNullHandling: typeof opts.strictNullHandling === "boolean" ? opts.strictNullHandling : defaults2.strictNullHandling,
        throwOnLimitExceeded: typeof opts.throwOnLimitExceeded === "boolean" ? opts.throwOnLimitExceeded : false
      };
    };
    module.exports = function(str, opts) {
      var options = normalizeParseOptions(opts);
      if (str === "" || str === null || typeof str === "undefined") {
        return options.plainObjects ? { __proto__: null } : {};
      }
      var tempObj = typeof str === "string" ? parseValues(str, options) : str;
      var obj = options.plainObjects ? { __proto__: null } : {};
      var keys2 = Object.keys(tempObj);
      for (var i = 0; i < keys2.length; ++i) {
        var key = keys2[i];
        var newObj = parseKeys(key, tempObj[key], options, typeof str === "string");
        obj = utils.merge(obj, newObj, options);
      }
      if (options.allowSparse === true) {
        return obj;
      }
      return utils.compact(obj);
    };
  }
});

// node_modules/qs/lib/index.js
var require_lib = __commonJS({
  "node_modules/qs/lib/index.js"(exports, module) {
    "use strict";
    var stringify2 = require_stringify();
    var parse2 = require_parse();
    var formats = require_formats();
    module.exports = {
      formats,
      parse: parse2,
      stringify: stringify2
    };
  }
});

// node_modules/es-toolkit/dist/array/chunk.mjs
function chunk(arr, size2) {
  if (!Number.isInteger(size2) || size2 <= 0) {
    throw new Error("Size must be an integer greater than zero.");
  }
  const chunkLength = Math.ceil(arr.length / size2);
  const result2 = Array(chunkLength);
  for (let index = 0; index < chunkLength; index++) {
    const start = index * size2;
    const end = start + size2;
    result2[index] = arr.slice(start, end);
  }
  return result2;
}

// node_modules/es-toolkit/dist/array/compact.mjs
function compact(arr) {
  const result2 = [];
  for (let i = 0; i < arr.length; i++) {
    const item = arr[i];
    if (item) {
      result2.push(item);
    }
  }
  return result2;
}

// node_modules/es-toolkit/dist/array/difference.mjs
function difference(firstArr, secondArr) {
  const secondSet = new Set(secondArr);
  return firstArr.filter((item) => !secondSet.has(item));
}

// node_modules/es-toolkit/dist/array/differenceBy.mjs
function differenceBy(firstArr, secondArr, mapper) {
  const mappedSecondSet = new Set(secondArr.map((item) => mapper(item)));
  return firstArr.filter((item) => {
    return !mappedSecondSet.has(mapper(item));
  });
}

// node_modules/es-toolkit/dist/array/differenceWith.mjs
function differenceWith(firstArr, secondArr, areItemsEqual) {
  return firstArr.filter((firstItem) => {
    return secondArr.every((secondItem) => {
      return !areItemsEqual(firstItem, secondItem);
    });
  });
}

// node_modules/es-toolkit/dist/array/drop.mjs
function drop(arr, itemsCount) {
  itemsCount = Math.max(itemsCount, 0);
  return arr.slice(itemsCount);
}

// node_modules/es-toolkit/dist/array/dropRight.mjs
function dropRight(arr, itemsCount) {
  itemsCount = Math.min(-itemsCount, 0);
  if (itemsCount === 0) {
    return arr.slice();
  }
  return arr.slice(0, itemsCount);
}

// node_modules/es-toolkit/dist/array/dropRightWhile.mjs
function dropRightWhile(arr, canContinueDropping) {
  for (let i = arr.length - 1; i >= 0; i--) {
    if (!canContinueDropping(arr[i], i, arr)) {
      return arr.slice(0, i + 1);
    }
  }
  return [];
}

// node_modules/es-toolkit/dist/array/dropWhile.mjs
function dropWhile(arr, canContinueDropping) {
  const dropEndIndex = arr.findIndex((item, index, arr2) => !canContinueDropping(item, index, arr2));
  if (dropEndIndex === -1) {
    return [];
  }
  return arr.slice(dropEndIndex);
}

// node_modules/es-toolkit/dist/array/fill.mjs
function fill(array, value, start = 0, end = array.length) {
  const length = array.length;
  const finalStart = Math.max(start >= 0 ? start : length + start, 0);
  const finalEnd = Math.min(end >= 0 ? end : length + end, length);
  for (let i = finalStart; i < finalEnd; i++) {
    array[i] = value;
  }
  return array;
}

// node_modules/es-toolkit/dist/array/flatten.mjs
function flatten(arr, depth = 1) {
  const result2 = [];
  const flooredDepth = Math.floor(depth);
  const recursive = (arr2, currentDepth) => {
    for (let i = 0; i < arr2.length; i++) {
      const item = arr2[i];
      if (Array.isArray(item) && currentDepth < flooredDepth) {
        recursive(item, currentDepth + 1);
      } else {
        result2.push(item);
      }
    }
  };
  recursive(arr, 0);
  return result2;
}

// node_modules/es-toolkit/dist/array/groupBy.mjs
function groupBy(arr, getKeyFromItem) {
  const result2 = {};
  for (let i = 0; i < arr.length; i++) {
    const item = arr[i];
    const key = getKeyFromItem(item);
    if (!Object.hasOwn(result2, key)) {
      result2[key] = [];
    }
    result2[key].push(item);
  }
  return result2;
}

// node_modules/es-toolkit/dist/array/head.mjs
function head(arr) {
  return arr[0];
}

// node_modules/es-toolkit/dist/array/initial.mjs
function initial(arr) {
  return arr.slice(0, -1);
}

// node_modules/es-toolkit/dist/array/intersection.mjs
function intersection(firstArr, secondArr) {
  const secondSet = new Set(secondArr);
  return firstArr.filter((item) => {
    return secondSet.has(item);
  });
}

// node_modules/es-toolkit/dist/array/intersectionBy.mjs
function intersectionBy(firstArr, secondArr, mapper) {
  const mappedSecondSet = new Set(secondArr.map(mapper));
  return firstArr.filter((item) => mappedSecondSet.has(mapper(item)));
}

// node_modules/es-toolkit/dist/array/intersectionWith.mjs
function intersectionWith(firstArr, secondArr, areItemsEqual) {
  return firstArr.filter((firstItem) => {
    return secondArr.some((secondItem) => {
      return areItemsEqual(firstItem, secondItem);
    });
  });
}

// node_modules/es-toolkit/dist/array/last.mjs
function last(arr) {
  return arr[arr.length - 1];
}

// node_modules/es-toolkit/dist/array/maxBy.mjs
function maxBy(items, getValue) {
  if (items.length === 0) {
    return void 0;
  }
  let maxElement = items[0];
  let max2 = getValue(maxElement);
  for (let i = 1; i < items.length; i++) {
    const element = items[i];
    const value = getValue(element);
    if (value > max2) {
      max2 = value;
      maxElement = element;
    }
  }
  return maxElement;
}

// node_modules/es-toolkit/dist/array/minBy.mjs
function minBy(items, getValue) {
  if (items.length === 0) {
    return void 0;
  }
  let minElement = items[0];
  let min2 = getValue(minElement);
  for (let i = 1; i < items.length; i++) {
    const element = items[i];
    const value = getValue(element);
    if (value < min2) {
      min2 = value;
      minElement = element;
    }
  }
  return minElement;
}

// node_modules/es-toolkit/dist/array/pull.mjs
function pull(arr, valuesToRemove) {
  const valuesSet = new Set(valuesToRemove);
  let resultIndex = 0;
  for (let i = 0; i < arr.length; i++) {
    if (valuesSet.has(arr[i])) {
      continue;
    }
    if (!Object.hasOwn(arr, i)) {
      delete arr[resultIndex++];
      continue;
    }
    arr[resultIndex++] = arr[i];
  }
  arr.length = resultIndex;
  return arr;
}

// node_modules/es-toolkit/dist/array/remove.mjs
function remove(arr, shouldRemoveElement) {
  const originalArr = arr.slice();
  const removed = [];
  let resultIndex = 0;
  for (let i = 0; i < arr.length; i++) {
    if (shouldRemoveElement(arr[i], i, originalArr)) {
      removed.push(arr[i]);
      continue;
    }
    if (!Object.hasOwn(arr, i)) {
      delete arr[resultIndex++];
      continue;
    }
    arr[resultIndex++] = arr[i];
  }
  arr.length = resultIndex;
  return removed;
}

// node_modules/es-toolkit/dist/array/sample.mjs
function sample(arr) {
  const randomIndex = Math.floor(Math.random() * arr.length);
  return arr[randomIndex];
}

// node_modules/es-toolkit/dist/math/random.mjs
function random(minimum, maximum) {
  if (maximum == null) {
    maximum = minimum;
    minimum = 0;
  }
  if (minimum >= maximum) {
    throw new Error("Invalid input: The maximum value must be greater than the minimum value.");
  }
  return Math.random() * (maximum - minimum) + minimum;
}

// node_modules/es-toolkit/dist/math/randomInt.mjs
function randomInt(minimum, maximum) {
  return Math.floor(random(minimum, maximum));
}

// node_modules/es-toolkit/dist/array/sampleSize.mjs
function sampleSize(array, size2) {
  if (size2 > array.length) {
    throw new Error("Size must be less than or equal to the length of array.");
  }
  const result2 = new Array(size2);
  const selected = /* @__PURE__ */ new Set();
  for (let step = array.length - size2, resultIndex = 0; step < array.length; step++, resultIndex++) {
    let index = randomInt(0, step + 1);
    if (selected.has(index)) {
      index = step;
    }
    selected.add(index);
    result2[resultIndex] = array[index];
  }
  return result2;
}

// node_modules/es-toolkit/dist/array/shuffle.mjs
function shuffle(arr) {
  const result2 = arr.slice();
  for (let i = result2.length - 1; i >= 1; i--) {
    const j2 = Math.floor(Math.random() * (i + 1));
    [result2[i], result2[j2]] = [result2[j2], result2[i]];
  }
  return result2;
}

// node_modules/es-toolkit/dist/array/tail.mjs
function tail(arr) {
  return arr.slice(1);
}

// node_modules/es-toolkit/dist/compat/predicate/isSymbol.mjs
function isSymbol(value) {
  return typeof value === "symbol" || value instanceof Symbol;
}

// node_modules/es-toolkit/dist/compat/util/toNumber.mjs
function toNumber(value) {
  if (isSymbol(value)) {
    return NaN;
  }
  return Number(value);
}

// node_modules/es-toolkit/dist/compat/util/toFinite.mjs
function toFinite(value) {
  if (!value) {
    return value === 0 ? value : 0;
  }
  value = toNumber(value);
  if (value === Infinity || value === -Infinity) {
    const sign = value < 0 ? -1 : 1;
    return sign * Number.MAX_VALUE;
  }
  return value === value ? value : 0;
}

// node_modules/es-toolkit/dist/compat/util/toInteger.mjs
function toInteger(value) {
  const finite = toFinite(value);
  const remainder = finite % 1;
  return remainder ? finite - remainder : finite;
}

// node_modules/es-toolkit/dist/array/take.mjs
function take(arr, count, guard) {
  count = guard || count === void 0 ? 1 : toInteger(count);
  return arr.slice(0, count);
}

// node_modules/es-toolkit/dist/array/takeRight.mjs
function takeRight(arr, count = 1, guard) {
  count = guard || count === void 0 ? 1 : toInteger(count);
  if (count <= 0 || arr == null || arr.length === 0) {
    return [];
  }
  return arr.slice(-count);
}

// node_modules/es-toolkit/dist/array/uniq.mjs
function uniq(arr) {
  return Array.from(new Set(arr));
}

// node_modules/es-toolkit/dist/array/uniqBy.mjs
function uniqBy(arr, mapper) {
  const map2 = /* @__PURE__ */ new Map();
  for (let i = 0; i < arr.length; i++) {
    const item = arr[i];
    const key = mapper(item);
    if (!map2.has(key)) {
      map2.set(key, item);
    }
  }
  return Array.from(map2.values());
}

// node_modules/es-toolkit/dist/array/uniqWith.mjs
function uniqWith(arr, areItemsEqual) {
  const result2 = [];
  for (let i = 0; i < arr.length; i++) {
    const item = arr[i];
    const isUniq = result2.every((v2) => !areItemsEqual(v2, item));
    if (isUniq) {
      result2.push(item);
    }
  }
  return result2;
}

// node_modules/es-toolkit/dist/array/unzip.mjs
function unzip(zipped) {
  let maxLen = 0;
  for (let i = 0; i < zipped.length; i++) {
    if (zipped[i].length > maxLen) {
      maxLen = zipped[i].length;
    }
  }
  const result2 = new Array(maxLen);
  for (let i = 0; i < maxLen; i++) {
    result2[i] = new Array(zipped.length);
    for (let j2 = 0; j2 < zipped.length; j2++) {
      result2[i][j2] = zipped[j2][i];
    }
  }
  return result2;
}

// node_modules/es-toolkit/dist/array/windowed.mjs
function windowed(arr, size2, step = 1, { partialWindows = false } = {}) {
  if (size2 <= 0 || !Number.isInteger(size2)) {
    throw new Error("Size must be a positive integer.");
  }
  if (step <= 0 || !Number.isInteger(step)) {
    throw new Error("Step must be a positive integer.");
  }
  const result2 = [];
  const end = partialWindows ? arr.length : arr.length - size2 + 1;
  for (let i = 0; i < end; i += step) {
    result2.push(arr.slice(i, i + size2));
  }
  return result2;
}

// node_modules/es-toolkit/dist/array/without.mjs
function without(array, ...values2) {
  return difference(array, values2);
}

// node_modules/es-toolkit/dist/array/zip.mjs
function zip(...arrs) {
  let rowCount = 0;
  for (let i = 0; i < arrs.length; i++) {
    if (arrs[i].length > rowCount) {
      rowCount = arrs[i].length;
    }
  }
  const columnCount = arrs.length;
  const result2 = Array(rowCount);
  for (let i = 0; i < rowCount; ++i) {
    const row = Array(columnCount);
    for (let j2 = 0; j2 < columnCount; ++j2) {
      row[j2] = arrs[j2][i];
    }
    result2[i] = row;
  }
  return result2;
}

// node_modules/es-toolkit/dist/function/after.mjs
function after(n2, func) {
  if (!Number.isInteger(n2) || n2 < 0) {
    throw new Error(`n must be a non-negative integer.`);
  }
  let counter = 0;
  return (...args) => {
    if (++counter >= n2) {
      return func(...args);
    }
    return void 0;
  };
}

// node_modules/es-toolkit/dist/function/ary.mjs
function ary(func, n2) {
  return function(...args) {
    return func.apply(this, args.slice(0, n2));
  };
}

// node_modules/es-toolkit/dist/function/debounce.mjs
function debounce(func, debounceMs, { signal, edges } = {}) {
  let pendingThis = void 0;
  let pendingArgs = null;
  const leading = edges != null && edges.includes("leading");
  const trailing = edges == null || edges.includes("trailing");
  const invoke2 = () => {
    if (pendingArgs !== null) {
      func.apply(pendingThis, pendingArgs);
      pendingThis = void 0;
      pendingArgs = null;
    }
  };
  const onTimerEnd = () => {
    if (trailing) {
      invoke2();
    }
    cancel();
  };
  let timeoutId = null;
  const schedule = () => {
    if (timeoutId != null) {
      clearTimeout(timeoutId);
    }
    timeoutId = setTimeout(() => {
      timeoutId = null;
      onTimerEnd();
    }, debounceMs);
  };
  const cancelTimer = () => {
    if (timeoutId !== null) {
      clearTimeout(timeoutId);
      timeoutId = null;
    }
  };
  const cancel = () => {
    cancelTimer();
    pendingThis = void 0;
    pendingArgs = null;
  };
  const flush = () => {
    cancelTimer();
    invoke2();
  };
  const debounced = function(...args) {
    if (signal == null ? void 0 : signal.aborted) {
      return;
    }
    pendingThis = this;
    pendingArgs = args;
    const isFirstCall = timeoutId == null;
    schedule();
    if (leading && isFirstCall) {
      invoke2();
    }
  };
  debounced.schedule = schedule;
  debounced.cancel = cancel;
  debounced.flush = flush;
  signal == null ? void 0 : signal.addEventListener("abort", cancel, { once: true });
  return debounced;
}

// node_modules/es-toolkit/dist/function/flow.mjs
function flow(...funcs) {
  return function(...args) {
    let result2 = funcs.length ? funcs[0].apply(this, args) : args[0];
    for (let i = 1; i < funcs.length; i++) {
      result2 = funcs[i].call(this, result2);
    }
    return result2;
  };
}

// node_modules/es-toolkit/dist/function/flowRight.mjs
function flowRight(...funcs) {
  return flow(...funcs.reverse());
}

// node_modules/es-toolkit/dist/function/identity.mjs
function identity(x3) {
  return x3;
}

// node_modules/es-toolkit/dist/function/negate.mjs
function negate(func) {
  return (...args) => !func(...args);
}

// node_modules/es-toolkit/dist/function/noop.mjs
function noop() {
}

// node_modules/es-toolkit/dist/function/once.mjs
function once(func) {
  let called = false;
  let cache;
  return function(...args) {
    if (!called) {
      called = true;
      cache = func(...args);
    }
    return cache;
  };
}

// node_modules/es-toolkit/dist/function/partial.mjs
function partial(func, ...partialArgs) {
  return partialImpl(func, placeholderSymbol, ...partialArgs);
}
function partialImpl(func, placeholder, ...partialArgs) {
  const partialed = function(...providedArgs) {
    let providedArgsIndex = 0;
    const substitutedArgs = partialArgs.slice().map((arg) => arg === placeholder ? providedArgs[providedArgsIndex++] : arg);
    const remainingArgs = providedArgs.slice(providedArgsIndex);
    return func.apply(this, substitutedArgs.concat(remainingArgs));
  };
  if (func.prototype) {
    partialed.prototype = Object.create(func.prototype);
  }
  return partialed;
}
var placeholderSymbol = Symbol("partial.placeholder");
partial.placeholder = placeholderSymbol;

// node_modules/es-toolkit/dist/function/partialRight.mjs
function partialRight(func, ...partialArgs) {
  return partialRightImpl(func, placeholderSymbol2, ...partialArgs);
}
function partialRightImpl(func, placeholder, ...partialArgs) {
  const partialedRight = function(...providedArgs) {
    const placeholderLength = partialArgs.filter((arg) => arg === placeholder).length;
    const rangeLength = Math.max(providedArgs.length - placeholderLength, 0);
    const remainingArgs = providedArgs.slice(0, rangeLength);
    let providedArgsIndex = rangeLength;
    const substitutedArgs = partialArgs.slice().map((arg) => arg === placeholder ? providedArgs[providedArgsIndex++] : arg);
    return func.apply(this, remainingArgs.concat(substitutedArgs));
  };
  if (func.prototype) {
    partialedRight.prototype = Object.create(func.prototype);
  }
  return partialedRight;
}
var placeholderSymbol2 = Symbol("partialRight.placeholder");
partialRight.placeholder = placeholderSymbol2;

// node_modules/es-toolkit/dist/function/rest.mjs
function rest(func, startIndex = func.length - 1) {
  return function(...args) {
    const rest3 = args.slice(startIndex);
    const params = args.slice(0, startIndex);
    while (params.length < startIndex) {
      params.push(void 0);
    }
    return func.apply(this, [...params, rest3]);
  };
}

// node_modules/es-toolkit/dist/function/retry.mjs
var DEFAULT_RETRIES = Number.POSITIVE_INFINITY;

// node_modules/es-toolkit/dist/function/unary.mjs
function unary(func) {
  return ary(func, 1);
}

// node_modules/es-toolkit/dist/math/clamp.mjs
function clamp(value, bound1, bound2) {
  if (bound2 == null) {
    return Math.min(value, bound1);
  }
  return Math.min(Math.max(value, bound1), bound2);
}

// node_modules/es-toolkit/dist/math/inRange.mjs
function inRange(value, minimum, maximum) {
  if (maximum == null) {
    maximum = minimum;
    minimum = 0;
  }
  if (minimum >= maximum) {
    throw new Error("The maximum value must be greater than the minimum value.");
  }
  return minimum <= value && value < maximum;
}

// node_modules/es-toolkit/dist/math/sum.mjs
function sum(nums) {
  let result2 = 0;
  for (let i = 0; i < nums.length; i++) {
    result2 += nums[i];
  }
  return result2;
}

// node_modules/es-toolkit/dist/math/mean.mjs
function mean(nums) {
  return sum(nums) / nums.length;
}

// node_modules/es-toolkit/dist/math/meanBy.mjs
function meanBy(items, getValue) {
  const nums = items.map((x3) => getValue(x3));
  return mean(nums);
}

// node_modules/es-toolkit/dist/math/range.mjs
function range(start, end, step = 1) {
  if (end == null) {
    end = start;
    start = 0;
  }
  if (!Number.isInteger(step) || step === 0) {
    throw new Error(`The step value must be a non-zero integer.`);
  }
  const length = Math.max(Math.ceil((end - start) / step), 0);
  const result2 = new Array(length);
  for (let i = 0; i < length; i++) {
    result2[i] = start + i * step;
  }
  return result2;
}

// node_modules/es-toolkit/dist/predicate/isPrimitive.mjs
function isPrimitive(value) {
  return value == null || typeof value !== "object" && typeof value !== "function";
}

// node_modules/es-toolkit/dist/predicate/isTypedArray.mjs
function isTypedArray(x3) {
  return ArrayBuffer.isView(x3) && !(x3 instanceof DataView);
}

// node_modules/es-toolkit/dist/object/clone.mjs
function clone(obj) {
  if (isPrimitive(obj)) {
    return obj;
  }
  if (Array.isArray(obj) || isTypedArray(obj) || obj instanceof ArrayBuffer || typeof SharedArrayBuffer !== "undefined" && obj instanceof SharedArrayBuffer) {
    return obj.slice(0);
  }
  const prototype = Object.getPrototypeOf(obj);
  const Constructor = prototype.constructor;
  if (obj instanceof Date || obj instanceof Map || obj instanceof Set) {
    return new Constructor(obj);
  }
  if (obj instanceof RegExp) {
    const newRegExp = new Constructor(obj);
    newRegExp.lastIndex = obj.lastIndex;
    return newRegExp;
  }
  if (obj instanceof DataView) {
    return new Constructor(obj.buffer.slice(0));
  }
  if (obj instanceof Error) {
    const newError = new Constructor(obj.message);
    newError.stack = obj.stack;
    newError.name = obj.name;
    newError.cause = obj.cause;
    return newError;
  }
  if (typeof File !== "undefined" && obj instanceof File) {
    const newFile = new Constructor([obj], obj.name, { type: obj.type, lastModified: obj.lastModified });
    return newFile;
  }
  if (typeof obj === "object") {
    const newObject = Object.create(prototype);
    return Object.assign(newObject, obj);
  }
  return obj;
}

// node_modules/es-toolkit/dist/compat/_internal/getSymbols.mjs
function getSymbols(object) {
  return Object.getOwnPropertySymbols(object).filter((symbol) => Object.prototype.propertyIsEnumerable.call(object, symbol));
}

// node_modules/es-toolkit/dist/compat/_internal/getTag.mjs
function getTag(value) {
  if (value == null) {
    return value === void 0 ? "[object Undefined]" : "[object Null]";
  }
  return Object.prototype.toString.call(value);
}

// node_modules/es-toolkit/dist/compat/_internal/tags.mjs
var regexpTag = "[object RegExp]";
var stringTag = "[object String]";
var numberTag = "[object Number]";
var booleanTag = "[object Boolean]";
var argumentsTag = "[object Arguments]";
var symbolTag = "[object Symbol]";
var dateTag = "[object Date]";
var mapTag = "[object Map]";
var setTag = "[object Set]";
var arrayTag = "[object Array]";
var functionTag = "[object Function]";
var arrayBufferTag = "[object ArrayBuffer]";
var objectTag = "[object Object]";
var errorTag = "[object Error]";
var dataViewTag = "[object DataView]";
var uint8ArrayTag = "[object Uint8Array]";
var uint8ClampedArrayTag = "[object Uint8ClampedArray]";
var uint16ArrayTag = "[object Uint16Array]";
var uint32ArrayTag = "[object Uint32Array]";
var bigUint64ArrayTag = "[object BigUint64Array]";
var int8ArrayTag = "[object Int8Array]";
var int16ArrayTag = "[object Int16Array]";
var int32ArrayTag = "[object Int32Array]";
var bigInt64ArrayTag = "[object BigInt64Array]";
var float32ArrayTag = "[object Float32Array]";
var float64ArrayTag = "[object Float64Array]";

// node_modules/es-toolkit/dist/object/cloneDeepWith.mjs
function cloneDeepWith(obj, cloneValue) {
  return cloneDeepWithImpl(obj, void 0, obj, /* @__PURE__ */ new Map(), cloneValue);
}
function cloneDeepWithImpl(valueToClone, keyToClone, objectToClone, stack = /* @__PURE__ */ new Map(), cloneValue = void 0) {
  const cloned = cloneValue == null ? void 0 : cloneValue(valueToClone, keyToClone, objectToClone, stack);
  if (cloned != null) {
    return cloned;
  }
  if (isPrimitive(valueToClone)) {
    return valueToClone;
  }
  if (stack.has(valueToClone)) {
    return stack.get(valueToClone);
  }
  if (Array.isArray(valueToClone)) {
    const result2 = new Array(valueToClone.length);
    stack.set(valueToClone, result2);
    for (let i = 0; i < valueToClone.length; i++) {
      result2[i] = cloneDeepWithImpl(valueToClone[i], i, objectToClone, stack, cloneValue);
    }
    if (Object.hasOwn(valueToClone, "index")) {
      result2.index = valueToClone.index;
    }
    if (Object.hasOwn(valueToClone, "input")) {
      result2.input = valueToClone.input;
    }
    return result2;
  }
  if (valueToClone instanceof Date) {
    return new Date(valueToClone.getTime());
  }
  if (valueToClone instanceof RegExp) {
    const result2 = new RegExp(valueToClone.source, valueToClone.flags);
    result2.lastIndex = valueToClone.lastIndex;
    return result2;
  }
  if (valueToClone instanceof Map) {
    const result2 = /* @__PURE__ */ new Map();
    stack.set(valueToClone, result2);
    for (const [key, value] of valueToClone) {
      result2.set(key, cloneDeepWithImpl(value, key, objectToClone, stack, cloneValue));
    }
    return result2;
  }
  if (valueToClone instanceof Set) {
    const result2 = /* @__PURE__ */ new Set();
    stack.set(valueToClone, result2);
    for (const value of valueToClone) {
      result2.add(cloneDeepWithImpl(value, void 0, objectToClone, stack, cloneValue));
    }
    return result2;
  }
  if (typeof Buffer !== "undefined" && Buffer.isBuffer(valueToClone)) {
    return valueToClone.subarray();
  }
  if (isTypedArray(valueToClone)) {
    const result2 = new (Object.getPrototypeOf(valueToClone)).constructor(valueToClone.length);
    stack.set(valueToClone, result2);
    for (let i = 0; i < valueToClone.length; i++) {
      result2[i] = cloneDeepWithImpl(valueToClone[i], i, objectToClone, stack, cloneValue);
    }
    return result2;
  }
  if (valueToClone instanceof ArrayBuffer || typeof SharedArrayBuffer !== "undefined" && valueToClone instanceof SharedArrayBuffer) {
    return valueToClone.slice(0);
  }
  if (valueToClone instanceof DataView) {
    const result2 = new DataView(valueToClone.buffer.slice(0), valueToClone.byteOffset, valueToClone.byteLength);
    stack.set(valueToClone, result2);
    copyProperties(result2, valueToClone, objectToClone, stack, cloneValue);
    return result2;
  }
  if (typeof File !== "undefined" && valueToClone instanceof File) {
    const result2 = new File([valueToClone], valueToClone.name, {
      type: valueToClone.type
    });
    stack.set(valueToClone, result2);
    copyProperties(result2, valueToClone, objectToClone, stack, cloneValue);
    return result2;
  }
  if (valueToClone instanceof Blob) {
    const result2 = new Blob([valueToClone], { type: valueToClone.type });
    stack.set(valueToClone, result2);
    copyProperties(result2, valueToClone, objectToClone, stack, cloneValue);
    return result2;
  }
  if (valueToClone instanceof Error) {
    const result2 = new valueToClone.constructor();
    stack.set(valueToClone, result2);
    result2.message = valueToClone.message;
    result2.name = valueToClone.name;
    result2.stack = valueToClone.stack;
    result2.cause = valueToClone.cause;
    copyProperties(result2, valueToClone, objectToClone, stack, cloneValue);
    return result2;
  }
  if (typeof valueToClone === "object" && isCloneableObject(valueToClone)) {
    const result2 = Object.create(Object.getPrototypeOf(valueToClone));
    stack.set(valueToClone, result2);
    copyProperties(result2, valueToClone, objectToClone, stack, cloneValue);
    return result2;
  }
  return valueToClone;
}
function copyProperties(target, source, objectToClone = target, stack, cloneValue) {
  const keys2 = [...Object.keys(source), ...getSymbols(source)];
  for (let i = 0; i < keys2.length; i++) {
    const key = keys2[i];
    const descriptor = Object.getOwnPropertyDescriptor(target, key);
    if (descriptor == null || descriptor.writable) {
      target[key] = cloneDeepWithImpl(source[key], key, objectToClone, stack, cloneValue);
    }
  }
}
function isCloneableObject(object) {
  switch (getTag(object)) {
    case argumentsTag:
    case arrayTag:
    case arrayBufferTag:
    case dataViewTag:
    case booleanTag:
    case dateTag:
    case float32ArrayTag:
    case float64ArrayTag:
    case int8ArrayTag:
    case int16ArrayTag:
    case int32ArrayTag:
    case mapTag:
    case numberTag:
    case objectTag:
    case regexpTag:
    case setTag:
    case stringTag:
    case symbolTag:
    case uint8ArrayTag:
    case uint8ClampedArrayTag:
    case uint16ArrayTag:
    case uint32ArrayTag: {
      return true;
    }
    default: {
      return false;
    }
  }
}

// node_modules/es-toolkit/dist/object/cloneDeep.mjs
function cloneDeep(obj) {
  return cloneDeepWithImpl(obj, void 0, obj, /* @__PURE__ */ new Map(), void 0);
}

// node_modules/es-toolkit/dist/object/findKey.mjs
function findKey(obj, predicate) {
  const keys2 = Object.keys(obj);
  return keys2.find((key) => predicate(obj[key], key, obj));
}

// node_modules/es-toolkit/dist/predicate/isPlainObject.mjs
function isPlainObject(value) {
  if (!value || typeof value !== "object") {
    return false;
  }
  const proto = Object.getPrototypeOf(value);
  const hasObjectPrototype = proto === null || proto === Object.prototype || Object.getPrototypeOf(proto) === null;
  if (!hasObjectPrototype) {
    return false;
  }
  return Object.prototype.toString.call(value) === "[object Object]";
}

// node_modules/es-toolkit/dist/object/invert.mjs
function invert(obj) {
  const result2 = {};
  const keys2 = Object.keys(obj);
  for (let i = 0; i < keys2.length; i++) {
    const key = keys2[i];
    const value = obj[key];
    result2[value] = key;
  }
  return result2;
}

// node_modules/es-toolkit/dist/object/mapKeys.mjs
function mapKeys(object, getNewKey) {
  const result2 = {};
  const keys2 = Object.keys(object);
  for (let i = 0; i < keys2.length; i++) {
    const key = keys2[i];
    const value = object[key];
    result2[getNewKey(value, key, object)] = value;
  }
  return result2;
}

// node_modules/es-toolkit/dist/object/mapValues.mjs
function mapValues(object, getNewValue) {
  const result2 = {};
  const keys2 = Object.keys(object);
  for (let i = 0; i < keys2.length; i++) {
    const key = keys2[i];
    const value = object[key];
    result2[key] = getNewValue(value, key, object);
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/predicate/isObjectLike.mjs
function isObjectLike(value) {
  return typeof value === "object" && value !== null;
}

// node_modules/es-toolkit/dist/object/mergeWith.mjs
function mergeWith(target, source, merge3) {
  const sourceKeys = Object.keys(source);
  for (let i = 0; i < sourceKeys.length; i++) {
    const key = sourceKeys[i];
    const sourceValue = source[key];
    const targetValue = target[key];
    const merged = merge3(targetValue, sourceValue, key, target, source);
    if (merged != null) {
      target[key] = merged;
    } else if (Array.isArray(sourceValue)) {
      target[key] = mergeWith(targetValue ?? [], sourceValue, merge3);
    } else if (isObjectLike(targetValue) && isObjectLike(sourceValue)) {
      target[key] = mergeWith(targetValue ?? {}, sourceValue, merge3);
    } else if (targetValue === void 0 || sourceValue !== void 0) {
      target[key] = sourceValue;
    }
  }
  return target;
}

// node_modules/es-toolkit/dist/compat/predicate/isArray.mjs
function isArray(value) {
  return Array.isArray(value);
}

// node_modules/es-toolkit/dist/string/capitalize.mjs
function capitalize(str) {
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
}

// node_modules/es-toolkit/dist/string/words.mjs
var CASE_SPLIT_PATTERN = new RegExp("\\p{Lu}?\\p{Ll}+|[0-9]+|\\p{Lu}+(?!\\p{Ll})|\\p{Emoji_Presentation}|\\p{Extended_Pictographic}|\\p{L}+", "gu");
function words(str) {
  return Array.from(str.match(CASE_SPLIT_PATTERN) ?? []);
}

// node_modules/es-toolkit/dist/string/camelCase.mjs
function camelCase(str) {
  const words$1 = words(str);
  if (words$1.length === 0) {
    return "";
  }
  const [first, ...rest3] = words$1;
  return `${first.toLowerCase()}${rest3.map((word) => capitalize(word)).join("")}`;
}

// node_modules/es-toolkit/dist/compat/predicate/isPlainObject.mjs
function isPlainObject2(object) {
  var _a;
  if (typeof object !== "object") {
    return false;
  }
  if (object == null) {
    return false;
  }
  if (Object.getPrototypeOf(object) === null) {
    return true;
  }
  if (Object.prototype.toString.call(object) !== "[object Object]") {
    const tag = object[Symbol.toStringTag];
    if (tag == null) {
      return false;
    }
    const isTagReadonly = !((_a = Object.getOwnPropertyDescriptor(object, Symbol.toStringTag)) == null ? void 0 : _a.writable);
    if (isTagReadonly) {
      return false;
    }
    return object.toString() === `[object ${tag}]`;
  }
  let proto = object;
  while (Object.getPrototypeOf(proto) !== null) {
    proto = Object.getPrototypeOf(proto);
  }
  return Object.getPrototypeOf(object) === proto;
}

// node_modules/es-toolkit/dist/string/snakeCase.mjs
function snakeCase(str) {
  const words$1 = words(str);
  return words$1.map((word) => word.toLowerCase()).join("_");
}

// node_modules/es-toolkit/dist/predicate/isArrayBuffer.mjs
function isArrayBuffer(value) {
  return value instanceof ArrayBuffer;
}

// node_modules/es-toolkit/dist/predicate/isBuffer.mjs
function isBuffer(x3) {
  return typeof Buffer !== "undefined" && Buffer.isBuffer(x3);
}

// node_modules/es-toolkit/dist/predicate/isDate.mjs
function isDate(value) {
  return value instanceof Date;
}

// node_modules/es-toolkit/dist/compat/util/eq.mjs
function eq(value, other) {
  return value === other || Number.isNaN(value) && Number.isNaN(other);
}

// node_modules/es-toolkit/dist/predicate/isEqualWith.mjs
function isEqualWith(a2, b2, areValuesEqual) {
  return isEqualWithImpl(a2, b2, void 0, void 0, void 0, void 0, areValuesEqual);
}
function isEqualWithImpl(a2, b2, property2, aParent, bParent, stack, areValuesEqual) {
  const result2 = areValuesEqual(a2, b2, property2, aParent, bParent, stack);
  if (result2 !== void 0) {
    return result2;
  }
  if (typeof a2 === typeof b2) {
    switch (typeof a2) {
      case "bigint":
      case "string":
      case "boolean":
      case "symbol":
      case "undefined": {
        return a2 === b2;
      }
      case "number": {
        return a2 === b2 || Object.is(a2, b2);
      }
      case "function": {
        return a2 === b2;
      }
      case "object": {
        return areObjectsEqual(a2, b2, stack, areValuesEqual);
      }
    }
  }
  return areObjectsEqual(a2, b2, stack, areValuesEqual);
}
function areObjectsEqual(a2, b2, stack, areValuesEqual) {
  if (Object.is(a2, b2)) {
    return true;
  }
  let aTag = getTag(a2);
  let bTag = getTag(b2);
  if (aTag === argumentsTag) {
    aTag = objectTag;
  }
  if (bTag === argumentsTag) {
    bTag = objectTag;
  }
  if (aTag !== bTag) {
    return false;
  }
  switch (aTag) {
    case stringTag:
      return a2.toString() === b2.toString();
    case numberTag: {
      const x3 = a2.valueOf();
      const y2 = b2.valueOf();
      return eq(x3, y2);
    }
    case booleanTag:
    case dateTag:
    case symbolTag:
      return Object.is(a2.valueOf(), b2.valueOf());
    case regexpTag: {
      return a2.source === b2.source && a2.flags === b2.flags;
    }
    case functionTag: {
      return a2 === b2;
    }
  }
  stack = stack ?? /* @__PURE__ */ new Map();
  const aStack = stack.get(a2);
  const bStack = stack.get(b2);
  if (aStack != null && bStack != null) {
    return aStack === b2;
  }
  stack.set(a2, b2);
  stack.set(b2, a2);
  try {
    switch (aTag) {
      case mapTag: {
        if (a2.size !== b2.size) {
          return false;
        }
        for (const [key, value] of a2.entries()) {
          if (!b2.has(key) || !isEqualWithImpl(value, b2.get(key), key, a2, b2, stack, areValuesEqual)) {
            return false;
          }
        }
        return true;
      }
      case setTag: {
        if (a2.size !== b2.size) {
          return false;
        }
        const aValues = Array.from(a2.values());
        const bValues = Array.from(b2.values());
        for (let i = 0; i < aValues.length; i++) {
          const aValue = aValues[i];
          const index = bValues.findIndex((bValue) => {
            return isEqualWithImpl(aValue, bValue, void 0, a2, b2, stack, areValuesEqual);
          });
          if (index === -1) {
            return false;
          }
          bValues.splice(index, 1);
        }
        return true;
      }
      case arrayTag:
      case uint8ArrayTag:
      case uint8ClampedArrayTag:
      case uint16ArrayTag:
      case uint32ArrayTag:
      case bigUint64ArrayTag:
      case int8ArrayTag:
      case int16ArrayTag:
      case int32ArrayTag:
      case bigInt64ArrayTag:
      case float32ArrayTag:
      case float64ArrayTag: {
        if (typeof Buffer !== "undefined" && Buffer.isBuffer(a2) !== Buffer.isBuffer(b2)) {
          return false;
        }
        if (a2.length !== b2.length) {
          return false;
        }
        for (let i = 0; i < a2.length; i++) {
          if (!isEqualWithImpl(a2[i], b2[i], i, a2, b2, stack, areValuesEqual)) {
            return false;
          }
        }
        return true;
      }
      case arrayBufferTag: {
        if (a2.byteLength !== b2.byteLength) {
          return false;
        }
        return areObjectsEqual(new Uint8Array(a2), new Uint8Array(b2), stack, areValuesEqual);
      }
      case dataViewTag: {
        if (a2.byteLength !== b2.byteLength || a2.byteOffset !== b2.byteOffset) {
          return false;
        }
        return areObjectsEqual(new Uint8Array(a2), new Uint8Array(b2), stack, areValuesEqual);
      }
      case errorTag: {
        return a2.name === b2.name && a2.message === b2.message;
      }
      case objectTag: {
        const areEqualInstances = areObjectsEqual(a2.constructor, b2.constructor, stack, areValuesEqual) || isPlainObject(a2) && isPlainObject(b2);
        if (!areEqualInstances) {
          return false;
        }
        const aKeys = [...Object.keys(a2), ...getSymbols(a2)];
        const bKeys = [...Object.keys(b2), ...getSymbols(b2)];
        if (aKeys.length !== bKeys.length) {
          return false;
        }
        for (let i = 0; i < aKeys.length; i++) {
          const propKey = aKeys[i];
          const aProp = a2[propKey];
          if (!Object.hasOwn(b2, propKey)) {
            return false;
          }
          const bProp = b2[propKey];
          if (!isEqualWithImpl(aProp, bProp, propKey, a2, b2, stack, areValuesEqual)) {
            return false;
          }
        }
        return true;
      }
      default: {
        return false;
      }
    }
  } finally {
    stack.delete(a2);
    stack.delete(b2);
  }
}

// node_modules/es-toolkit/dist/predicate/isEqual.mjs
function isEqual(a2, b2) {
  return isEqualWith(a2, b2, noop);
}

// node_modules/es-toolkit/dist/predicate/isFunction.mjs
function isFunction(value) {
  return typeof value === "function";
}

// node_modules/es-toolkit/dist/predicate/isLength.mjs
function isLength(value) {
  return Number.isSafeInteger(value) && value >= 0;
}

// node_modules/es-toolkit/dist/predicate/isMap.mjs
function isMap(value) {
  return value instanceof Map;
}

// node_modules/es-toolkit/dist/predicate/isNil.mjs
function isNil(x3) {
  return x3 == null;
}

// node_modules/es-toolkit/dist/predicate/isNull.mjs
function isNull(x3) {
  return x3 === null;
}

// node_modules/es-toolkit/dist/predicate/isRegExp.mjs
function isRegExp(value) {
  return value instanceof RegExp;
}

// node_modules/es-toolkit/dist/predicate/isSet.mjs
function isSet(value) {
  return value instanceof Set;
}

// node_modules/es-toolkit/dist/predicate/isSymbol.mjs
function isSymbol2(value) {
  return typeof value === "symbol";
}

// node_modules/es-toolkit/dist/predicate/isUndefined.mjs
function isUndefined(x3) {
  return x3 === void 0;
}

// node_modules/es-toolkit/dist/predicate/isWeakMap.mjs
function isWeakMap(value) {
  return value instanceof WeakMap;
}

// node_modules/es-toolkit/dist/predicate/isWeakSet.mjs
function isWeakSet(value) {
  return value instanceof WeakSet;
}

// node_modules/es-toolkit/dist/string/deburr.mjs
var deburrMap = new Map(Object.entries({
  Æ: "Ae",
  Ð: "D",
  Ø: "O",
  Þ: "Th",
  ß: "ss",
  æ: "ae",
  ð: "d",
  ø: "o",
  þ: "th",
  Đ: "D",
  đ: "d",
  Ħ: "H",
  ħ: "h",
  ı: "i",
  Ĳ: "IJ",
  ĳ: "ij",
  ĸ: "k",
  Ŀ: "L",
  ŀ: "l",
  Ł: "L",
  ł: "l",
  ŉ: "'n",
  Ŋ: "N",
  ŋ: "n",
  Œ: "Oe",
  œ: "oe",
  Ŧ: "T",
  ŧ: "t",
  ſ: "s"
}));
function deburr(str) {
  str = str.normalize("NFD");
  let result2 = "";
  for (let i = 0; i < str.length; i++) {
    const char = str[i];
    if (char >= "̀" && char <= "ͯ" || char >= "︠" && char <= "︣") {
      continue;
    }
    result2 += deburrMap.get(char) ?? char;
  }
  return result2;
}

// node_modules/es-toolkit/dist/string/escape.mjs
var htmlEscapes = {
  "&": "&amp;",
  "<": "&lt;",
  ">": "&gt;",
  '"': "&quot;",
  "'": "&#39;"
};
function escape2(str) {
  return str.replace(/[&<>"']/g, (match) => htmlEscapes[match]);
}

// node_modules/es-toolkit/dist/string/escapeRegExp.mjs
function escapeRegExp(str) {
  return str.replace(/[\\^$.*+?()[\]{}|]/g, "\\$&");
}

// node_modules/es-toolkit/dist/string/kebabCase.mjs
function kebabCase(str) {
  const words$1 = words(str);
  return words$1.map((word) => word.toLowerCase()).join("-");
}

// node_modules/es-toolkit/dist/string/lowerCase.mjs
function lowerCase(str) {
  const words$1 = words(str);
  return words$1.map((word) => word.toLowerCase()).join(" ");
}

// node_modules/es-toolkit/dist/string/lowerFirst.mjs
function lowerFirst(str) {
  return str.substring(0, 1).toLowerCase() + str.substring(1);
}

// node_modules/es-toolkit/dist/string/pad.mjs
function pad(str, length, chars = " ") {
  return str.padStart(Math.floor((length - str.length) / 2) + str.length, chars).padEnd(length, chars);
}

// node_modules/es-toolkit/dist/string/trimEnd.mjs
function trimEnd(str, chars) {
  if (chars === void 0) {
    return str.trimEnd();
  }
  let endIndex = str.length;
  switch (typeof chars) {
    case "string": {
      if (chars.length !== 1) {
        throw new Error(`The 'chars' parameter should be a single character string.`);
      }
      while (endIndex > 0 && str[endIndex - 1] === chars) {
        endIndex--;
      }
      break;
    }
    case "object": {
      while (endIndex > 0 && chars.includes(str[endIndex - 1])) {
        endIndex--;
      }
    }
  }
  return str.substring(0, endIndex);
}

// node_modules/es-toolkit/dist/string/trimStart.mjs
function trimStart(str, chars) {
  if (chars === void 0) {
    return str.trimStart();
  }
  let startIndex = 0;
  switch (typeof chars) {
    case "string": {
      while (startIndex < str.length && str[startIndex] === chars) {
        startIndex++;
      }
      break;
    }
    case "object": {
      while (startIndex < str.length && chars.includes(str[startIndex])) {
        startIndex++;
      }
    }
  }
  return str.substring(startIndex);
}

// node_modules/es-toolkit/dist/string/trim.mjs
function trim(str, chars) {
  if (chars === void 0) {
    return str.trim();
  }
  return trimStart(trimEnd(str, chars), chars);
}

// node_modules/es-toolkit/dist/string/unescape.mjs
var htmlUnescapes = {
  "&amp;": "&",
  "&lt;": "<",
  "&gt;": ">",
  "&quot;": '"',
  "&#39;": "'"
};
function unescape2(str) {
  return str.replace(/&(?:amp|lt|gt|quot|#(0+)?39);/g, (match) => htmlUnescapes[match] || "'");
}

// node_modules/es-toolkit/dist/string/upperCase.mjs
function upperCase(str) {
  const words$1 = words(str);
  let result2 = "";
  for (let i = 0; i < words$1.length; i++) {
    result2 += words$1[i].toUpperCase();
    if (i < words$1.length - 1) {
      result2 += " ";
    }
  }
  return result2;
}

// node_modules/es-toolkit/dist/string/upperFirst.mjs
function upperFirst(str) {
  return str.substring(0, 1).toUpperCase() + str.substring(1);
}

// node_modules/@inertiajs/core/dist/index.esm.js
var $ = __toESM(require_lib());
function V(r4, e) {
  let t;
  return function(...i) {
    clearTimeout(t), t = setTimeout(() => r4.apply(this, i), e);
  };
}
function S(r4, e) {
  return document.dispatchEvent(new CustomEvent(`inertia:${r4}`, e));
}
var Y = (r4) => S("before", { cancelable: true, detail: { visit: r4 } });
var pe = (r4) => S("error", { detail: { errors: r4 } });
var de = (r4) => S("exception", { cancelable: true, detail: { exception: r4 } });
var he = (r4) => S("finish", { detail: { visit: r4 } });
var me = (r4) => S("invalid", { cancelable: true, detail: { response: r4 } });
var T = (r4) => S("navigate", { detail: { page: r4 } });
var fe = (r4) => S("progress", { detail: { progress: r4 } });
var ge = (r4) => S("start", { detail: { visit: r4 } });
var ve = (r4) => S("success", { detail: { page: r4 } });
var be = (r4, e) => S("prefetched", { detail: { fetchedAt: Date.now(), response: r4.data, visit: e } });
var ye = (r4) => S("prefetching", { detail: { visit: r4 } });
var m = class {
  static set(e, t) {
    typeof window < "u" && window.sessionStorage.setItem(e, JSON.stringify(t));
  }
  static get(e) {
    if (typeof window < "u") return JSON.parse(window.sessionStorage.getItem(e) || "null");
  }
  static merge(e, t) {
    let i = this.get(e);
    i === null ? this.set(e, t) : this.set(e, { ...i, ...t });
  }
  static remove(e) {
    typeof window < "u" && window.sessionStorage.removeItem(e);
  }
  static removeNested(e, t) {
    let i = this.get(e);
    i !== null && (delete i[t], this.set(e, i));
  }
  static exists(e) {
    try {
      return this.get(e) !== null;
    } catch {
      return false;
    }
  }
  static clear() {
    typeof window < "u" && window.sessionStorage.clear();
  }
};
m.locationVisitKey = "inertiaLocationVisit";
var Pe = async (r4) => {
  if (typeof window > "u") throw new Error("Unable to encrypt history");
  let e = Se(), t = await Ee(), i = await je(t);
  if (!i) throw new Error("Unable to encrypt history");
  return await $e(e, i, r4);
};
var C = { key: "historyKey", iv: "historyIv" };
var we = async (r4) => {
  let e = Se(), t = await Ee();
  if (!t) throw new Error("Unable to decrypt history");
  return await Be(e, t, r4);
};
var $e = async (r4, e, t) => {
  if (typeof window > "u") throw new Error("Unable to encrypt history");
  if (typeof window.crypto.subtle > "u") return console.warn("Encryption is not supported in this environment. SSL is required."), Promise.resolve(t);
  let i = new TextEncoder(), s = JSON.stringify(t), o = new Uint8Array(s.length * 3), u = i.encodeInto(s, o);
  return window.crypto.subtle.encrypt({ name: "AES-GCM", iv: r4 }, e, o.subarray(0, u.written));
};
var Be = async (r4, e, t) => {
  if (typeof window.crypto.subtle > "u") return console.warn("Decryption is not supported in this environment. SSL is required."), Promise.resolve(t);
  let i = await window.crypto.subtle.decrypt({ name: "AES-GCM", iv: r4 }, e, t);
  return JSON.parse(new TextDecoder().decode(i));
};
var Se = () => {
  let r4 = m.get(C.iv);
  if (r4) return new Uint8Array(r4);
  let e = window.crypto.getRandomValues(new Uint8Array(12));
  return m.set(C.iv, Array.from(e)), e;
};
var Ge = async () => typeof window.crypto.subtle > "u" ? (console.warn("Encryption is not supported in this environment. SSL is required."), Promise.resolve(null)) : window.crypto.subtle.generateKey({ name: "AES-GCM", length: 256 }, true, ["encrypt", "decrypt"]);
var Ke = async (r4) => {
  if (typeof window.crypto.subtle > "u") return console.warn("Encryption is not supported in this environment. SSL is required."), Promise.resolve();
  let e = await window.crypto.subtle.exportKey("raw", r4);
  m.set(C.key, Array.from(new Uint8Array(e)));
};
var je = async (r4) => {
  if (r4) return r4;
  let e = await Ge();
  return e ? (await Ke(e), e) : null;
};
var Ee = async () => {
  let r4 = m.get(C.key);
  return r4 ? await window.crypto.subtle.importKey("raw", new Uint8Array(r4), { name: "AES-GCM", length: 256 }, true, ["encrypt", "decrypt"]) : null;
};
var v = class {
  static save() {
    a.saveScrollPositions(Array.from(this.regions()).map((e) => ({ top: e.scrollTop, left: e.scrollLeft })));
  }
  static regions() {
    return document.querySelectorAll("[scroll-region]");
  }
  static reset() {
    typeof window < "u" && window.scrollTo(0, 0), this.regions().forEach((e) => {
      typeof e.scrollTo == "function" ? e.scrollTo(0, 0) : (e.scrollTop = 0, e.scrollLeft = 0);
    }), this.save(), window.location.hash && setTimeout(() => {
      var _a;
      return (_a = document.getElementById(window.location.hash.slice(1))) == null ? void 0 : _a.scrollIntoView();
    });
  }
  static restore(e) {
    this.restoreDocument(), this.regions().forEach((t, i) => {
      let s = e[i];
      s && (typeof t.scrollTo == "function" ? t.scrollTo(s.left, s.top) : (t.scrollTop = s.top, t.scrollLeft = s.left));
    });
  }
  static restoreDocument() {
    let e = a.getDocumentScrollPosition();
    typeof window < "u" && window.scrollTo(e.left, e.top);
  }
  static onScroll(e) {
    let t = e.target;
    typeof t.hasAttribute == "function" && t.hasAttribute("scroll-region") && this.save();
  }
  static onWindowScroll() {
    a.saveDocumentScrollPosition({ top: window.scrollY, left: window.scrollX });
  }
};
function N(r4) {
  return r4 instanceof File || r4 instanceof Blob || r4 instanceof FileList && r4.length > 0 || r4 instanceof FormData && Array.from(r4.values()).some((e) => N(e)) || typeof r4 == "object" && r4 !== null && Object.values(r4).some((e) => N(e));
}
var Z = (r4) => r4 instanceof FormData;
function ee(r4, e = new FormData(), t = null) {
  r4 = r4 || {};
  for (let i in r4) Object.prototype.hasOwnProperty.call(r4, i) && Te(e, Re(t, i), r4[i]);
  return e;
}
function Re(r4, e) {
  return r4 ? r4 + "[" + e + "]" : e;
}
function Te(r4, e, t) {
  if (Array.isArray(t)) return Array.from(t.keys()).forEach((i) => Te(r4, Re(e, i.toString()), t[i]));
  if (t instanceof Date) return r4.append(e, t.toISOString());
  if (t instanceof File) return r4.append(e, t, t.name);
  if (t instanceof Blob) return r4.append(e, t);
  if (typeof t == "boolean") return r4.append(e, t ? "1" : "0");
  if (typeof t == "string") return r4.append(e, t);
  if (typeof t == "number") return r4.append(e, `${t}`);
  if (t == null) return r4.append(e, "");
  ee(t, r4, e);
}
function y(r4) {
  return new URL(r4.toString(), typeof window > "u" ? void 0 : window.location.toString());
}
var xe = (r4, e, t, i, s) => {
  let o = typeof r4 == "string" ? y(r4) : r4;
  if ((N(e) || i) && !Z(e) && (e = ee(e)), Z(e)) return [o, e];
  let [u, d] = qe(t, o, e, s);
  return [y(u), d];
};
function qe(r4, e, t, i = "brackets") {
  let s = /^[a-z][a-z0-9+.-]*:\/\//i.test(e.toString()), o = s || e.toString().startsWith("/"), u = !o && !e.toString().startsWith("#") && !e.toString().startsWith("?"), d = e.toString().includes("?") || r4 === "get" && Object.keys(t).length, p = e.toString().includes("#"), c = new URL(e.toString(), "http://localhost");
  return r4 === "get" && Object.keys(t).length && (c.search = $.stringify(mergeWith($.parse(c.search, { ignoreQueryPrefix: true }), t, (l, h, I, w) => {
    h === void 0 && delete w[I];
  }), { encodeValuesOnly: true, arrayFormat: i }), t = {}), [[s ? `${c.protocol}//${c.host}` : "", o ? c.pathname : "", u ? c.pathname.substring(1) : "", d ? c.search : "", p ? c.hash : ""].join(""), t];
}
function F(r4) {
  return r4 = new URL(r4.href), r4.hash = "", r4;
}
var te = (r4, e) => {
  r4.hash && !e.hash && F(r4).href === e.href && (e.hash = r4.hash);
};
var L = (r4, e) => F(r4).href === F(e).href;
var re = class {
  constructor() {
    this.componentId = {};
    this.listeners = [];
    this.isFirstPageLoad = true;
    this.cleared = false;
  }
  init({ initialPage: e, swapComponent: t, resolveComponent: i }) {
    return this.page = e, this.swapComponent = t, this.resolveComponent = i, this;
  }
  set(e, { replace: t = false, preserveScroll: i = false, preserveState: s = false } = {}) {
    this.componentId = {};
    let o = this.componentId;
    return e.clearHistory && a.clear(), this.resolve(e.component).then((u) => {
      if (o !== this.componentId) return;
      e.rememberedState ?? (e.rememberedState = {});
      let d = typeof window < "u" ? window.location : new URL(e.url);
      return t = t || L(y(e.url), d), new Promise((p) => {
        t ? a.replaceState(e, () => p(null)) : a.pushState(e, () => p(null));
      }).then(() => {
        let p = !this.isTheSame(e);
        return this.page = e, this.cleared = false, p && this.fireEventsFor("newComponent"), this.isFirstPageLoad && this.fireEventsFor("firstLoad"), this.isFirstPageLoad = false, this.swap({ component: u, page: e, preserveState: s }).then(() => {
          i || v.reset(), E.fireInternalEvent("loadDeferredProps"), t || T(e);
        });
      });
    });
  }
  setQuietly(e, { preserveState: t = false } = {}) {
    return this.resolve(e.component).then((i) => (this.page = e, this.cleared = false, a.setCurrent(e), this.swap({ component: i, page: e, preserveState: t })));
  }
  clear() {
    this.cleared = true;
  }
  isCleared() {
    return this.cleared;
  }
  get() {
    return this.page;
  }
  merge(e) {
    this.page = { ...this.page, ...e };
  }
  setUrlHash(e) {
    this.page.url.includes(e) || (this.page.url += e);
  }
  remember(e) {
    this.page.rememberedState = e;
  }
  swap({ component: e, page: t, preserveState: i }) {
    return this.swapComponent({ component: e, page: t, preserveState: i });
  }
  resolve(e) {
    return Promise.resolve(this.resolveComponent(e));
  }
  isTheSame(e) {
    return this.page.component === e.component;
  }
  on(e, t) {
    return this.listeners.push({ event: e, callback: t }), () => {
      this.listeners = this.listeners.filter((i) => i.event !== e && i.callback !== t);
    };
  }
  fireEventsFor(e) {
    this.listeners.filter((t) => t.event === e).forEach((t) => t.callback());
  }
};
var n = new re();
var A = class {
  constructor() {
    this.items = [];
    this.processingPromise = null;
  }
  add(e) {
    return this.items.push(e), this.process();
  }
  process() {
    return this.processingPromise ?? (this.processingPromise = this.processNext().then(() => {
      this.processingPromise = null;
    })), this.processingPromise;
  }
  processNext() {
    let e = this.items.shift();
    return e ? Promise.resolve(e()).then(() => this.processNext()) : Promise.resolve();
  }
};
var O = typeof window > "u";
var k = new A();
var Ce = !O && /CriOS/.test(window.navigator.userAgent);
var ie = class {
  constructor() {
    this.rememberedState = "rememberedState";
    this.scrollRegions = "scrollRegions";
    this.preserveUrl = false;
    this.current = {};
    this.initialState = null;
  }
  remember(e, t) {
    var _a;
    this.replaceState({ ...n.get(), rememberedState: { ...((_a = n.get()) == null ? void 0 : _a.rememberedState) ?? {}, [t]: e } });
  }
  restore(e) {
    var _a, _b;
    if (!O) return (_b = (_a = this.initialState) == null ? void 0 : _a[this.rememberedState]) == null ? void 0 : _b[e];
  }
  pushState(e, t = null) {
    if (!O) {
      if (this.preserveUrl) {
        t && t();
        return;
      }
      this.current = e, k.add(() => this.getPageData(e).then((i) => {
        let s = () => {
          this.doPushState({ page: i }, e.url), t && t();
        };
        Ce ? setTimeout(s) : s();
      }));
    }
  }
  getPageData(e) {
    return new Promise((t) => e.encryptHistory ? Pe(e).then(t) : t(e));
  }
  processQueue() {
    return k.process();
  }
  decrypt(e = null) {
    var _a;
    if (O) return Promise.resolve(e ?? n.get());
    let t = e ?? ((_a = window.history.state) == null ? void 0 : _a.page);
    return this.decryptPageData(t).then((i) => {
      if (!i) throw new Error("Unable to decrypt history");
      return this.initialState === null ? this.initialState = i ?? void 0 : this.current = i ?? {}, i;
    });
  }
  decryptPageData(e) {
    return e instanceof ArrayBuffer ? we(e) : Promise.resolve(e);
  }
  saveScrollPositions(e) {
    k.add(() => Promise.resolve().then(() => {
      var _a;
      ((_a = window.history.state) == null ? void 0 : _a.page) && this.doReplaceState({ page: window.history.state.page, scrollRegions: e });
    }));
  }
  saveDocumentScrollPosition(e) {
    k.add(() => Promise.resolve().then(() => {
      var _a;
      ((_a = window.history.state) == null ? void 0 : _a.page) && this.doReplaceState({ page: window.history.state.page, documentScrollPosition: e });
    }));
  }
  getScrollRegions() {
    var _a;
    return ((_a = window.history.state) == null ? void 0 : _a.scrollRegions) || [];
  }
  getDocumentScrollPosition() {
    var _a;
    return ((_a = window.history.state) == null ? void 0 : _a.documentScrollPosition) || { top: 0, left: 0 };
  }
  replaceState(e, t = null) {
    if (n.merge(e), !O) {
      if (this.preserveUrl) {
        t && t();
        return;
      }
      this.current = e, k.add(() => this.getPageData(e).then((i) => {
        let s = () => {
          this.doReplaceState({ page: i }, e.url), t && t();
        };
        Ce ? setTimeout(s) : s();
      }));
    }
  }
  doReplaceState(e, t) {
    var _a, _b;
    window.history.replaceState({ ...e, scrollRegions: e.scrollRegions ?? ((_a = window.history.state) == null ? void 0 : _a.scrollRegions), documentScrollPosition: e.documentScrollPosition ?? ((_b = window.history.state) == null ? void 0 : _b.documentScrollPosition) }, "", t);
  }
  doPushState(e, t) {
    window.history.pushState(e, "", t);
  }
  getState(e, t) {
    var _a;
    return ((_a = this.current) == null ? void 0 : _a[e]) ?? t;
  }
  deleteState(e) {
    this.current[e] !== void 0 && (delete this.current[e], this.replaceState(this.current));
  }
  hasAnyState() {
    return !!this.getAllState();
  }
  clear() {
    m.remove(C.key), m.remove(C.iv);
  }
  setCurrent(e) {
    this.current = e;
  }
  isValidState(e) {
    return !!e.page;
  }
  getAllState() {
    return this.current;
  }
};
typeof window < "u" && window.history.scrollRestoration && (window.history.scrollRestoration = "manual");
var a = new ie();
var se = class {
  constructor() {
    this.internalListeners = [];
  }
  init() {
    typeof window < "u" && (window.addEventListener("popstate", this.handlePopstateEvent.bind(this)), window.addEventListener("scroll", V(v.onWindowScroll.bind(v), 100), true)), typeof document < "u" && document.addEventListener("scroll", V(v.onScroll.bind(v), 100), true);
  }
  onGlobalEvent(e, t) {
    let i = (s) => {
      let o = t(s);
      s.cancelable && !s.defaultPrevented && o === false && s.preventDefault();
    };
    return this.registerListener(`inertia:${e}`, i);
  }
  on(e, t) {
    return this.internalListeners.push({ event: e, listener: t }), () => {
      this.internalListeners = this.internalListeners.filter((i) => i.listener !== t);
    };
  }
  onMissingHistoryItem() {
    n.clear(), this.fireInternalEvent("missingHistoryItem");
  }
  fireInternalEvent(e) {
    this.internalListeners.filter((t) => t.event === e).forEach((t) => t.listener());
  }
  registerListener(e, t) {
    return document.addEventListener(e, t), () => document.removeEventListener(e, t);
  }
  handlePopstateEvent(e) {
    let t = e.state || null;
    if (t === null) {
      let i = y(n.get().url);
      i.hash = window.location.hash, a.replaceState({ ...n.get(), url: i.href }), v.reset();
      return;
    }
    if (!a.isValidState(t)) return this.onMissingHistoryItem();
    a.decrypt(t.page).then((i) => {
      if (n.get().version !== i.version) {
        this.onMissingHistoryItem();
        return;
      }
      n.setQuietly(i, { preserveState: false }).then(() => {
        v.restore(a.getScrollRegions()), T(n.get());
      });
    }).catch(() => {
      this.onMissingHistoryItem();
    });
  }
};
var E = new se();
var ne = class {
  constructor() {
    this.type = this.resolveType();
  }
  resolveType() {
    return typeof window > "u" ? "navigate" : window.performance && window.performance.getEntriesByType && window.performance.getEntriesByType("navigation").length > 0 ? window.performance.getEntriesByType("navigation")[0].type : "navigate";
  }
  get() {
    return this.type;
  }
  isBackForward() {
    return this.type === "back_forward";
  }
  isReload() {
    return this.type === "reload";
  }
};
var B = new ne();
var G = class {
  static handle() {
    this.clearRememberedStateOnReload(), [this.handleBackForward, this.handleLocation, this.handleDefault].find((t) => t.bind(this)());
  }
  static clearRememberedStateOnReload() {
    B.isReload() && a.deleteState(a.rememberedState);
  }
  static handleBackForward() {
    if (!B.isBackForward() || !a.hasAnyState()) return false;
    let e = a.getScrollRegions();
    return a.decrypt().then((t) => {
      n.set(t, { preserveScroll: true, preserveState: true }).then(() => {
        v.restore(e), T(n.get());
      });
    }).catch(() => {
      E.onMissingHistoryItem();
    }), true;
  }
  static handleLocation() {
    if (!m.exists(m.locationVisitKey)) return false;
    let e = m.get(m.locationVisitKey) || {};
    return m.remove(m.locationVisitKey), typeof window < "u" && n.setUrlHash(window.location.hash), a.decrypt(n.get()).then(() => {
      let t = a.getState(a.rememberedState, {}), i = a.getScrollRegions();
      n.remember(t), n.set(n.get(), { preserveScroll: e.preserveScroll, preserveState: true }).then(() => {
        e.preserveScroll && v.restore(i), T(n.get());
      });
    }).catch(() => {
      E.onMissingHistoryItem();
    }), true;
  }
  static handleDefault() {
    typeof window < "u" && n.setUrlHash(window.location.hash), n.set(n.get(), { preserveScroll: true, preserveState: true }).then(() => {
      B.isReload() && v.restore(a.getScrollRegions()), T(n.get());
    });
  }
};
var K = class {
  constructor(e, t, i) {
    this.id = null;
    this.throttle = false;
    this.keepAlive = false;
    this.cbCount = 0;
    this.keepAlive = i.keepAlive ?? false, this.cb = t, this.interval = e, (i.autoStart ?? true) && this.start();
  }
  stop() {
    this.id && clearInterval(this.id);
  }
  start() {
    typeof window > "u" || (this.stop(), this.id = window.setInterval(() => {
      (!this.throttle || this.cbCount % 10 === 0) && this.cb(), this.throttle && this.cbCount++;
    }, this.interval));
  }
  isInBackground(e) {
    this.throttle = this.keepAlive ? false : e, this.throttle && (this.cbCount = 0);
  }
};
var oe = class {
  constructor() {
    this.polls = [];
    this.setupVisibilityListener();
  }
  add(e, t, i) {
    let s = new K(e, t, i);
    return this.polls.push(s), { stop: () => s.stop(), start: () => s.start() };
  }
  clear() {
    this.polls.forEach((e) => e.stop()), this.polls = [];
  }
  setupVisibilityListener() {
    typeof document > "u" || document.addEventListener("visibilitychange", () => {
      this.polls.forEach((e) => e.isInBackground(document.hidden));
    }, false);
  }
};
var Ae = new oe();
var ae = (r4, e, t) => {
  if (r4 === e) return true;
  for (let i in r4) if (!t.includes(i) && r4[i] !== e[i] && !Xe(r4[i], e[i])) return false;
  return true;
};
var Xe = (r4, e) => {
  switch (typeof r4) {
    case "object":
      return ae(r4, e, []);
    case "function":
      return r4.toString() === e.toString();
    default:
      return r4 === e;
  }
};
var Je = { ms: 1, s: 1e3, m: 6e4, h: 36e5, d: 864e5 };
var le = (r4) => {
  if (typeof r4 == "number") return r4;
  for (let [e, t] of Object.entries(Je)) if (r4.endsWith(e)) return parseFloat(r4) * t;
  return parseInt(r4);
};
var ce = class {
  constructor() {
    this.cached = [];
    this.inFlightRequests = [];
    this.removalTimers = [];
    this.currentUseId = null;
  }
  add(e, t, { cacheFor: i }) {
    if (this.findInFlight(e)) return Promise.resolve();
    let o = this.findCached(e);
    if (!e.fresh && o && o.staleTimestamp > Date.now()) return Promise.resolve();
    let [u, d] = this.extractStaleValues(i), p = new Promise((c, l) => {
      t({ ...e, onCancel: () => {
        this.remove(e), e.onCancel(), l();
      }, onError: (h) => {
        this.remove(e), e.onError(h), l();
      }, onPrefetching(h) {
        e.onPrefetching(h);
      }, onPrefetched(h, I) {
        e.onPrefetched(h, I);
      }, onPrefetchResponse(h) {
        c(h);
      } });
    }).then((c) => (this.remove(e), this.cached.push({ params: { ...e }, staleTimestamp: Date.now() + u, response: p, singleUse: i === 0, timestamp: Date.now(), inFlight: false }), this.scheduleForRemoval(e, d), this.inFlightRequests = this.inFlightRequests.filter((l) => !this.paramsAreEqual(l.params, e)), c.handlePrefetch(), c));
    return this.inFlightRequests.push({ params: { ...e }, response: p, staleTimestamp: null, inFlight: true }), p;
  }
  removeAll() {
    this.cached = [], this.removalTimers.forEach((e) => {
      clearTimeout(e.timer);
    }), this.removalTimers = [];
  }
  remove(e) {
    this.cached = this.cached.filter((t) => !this.paramsAreEqual(t.params, e)), this.clearTimer(e);
  }
  extractStaleValues(e) {
    let [t, i] = this.cacheForToStaleAndExpires(e);
    return [le(t), le(i)];
  }
  cacheForToStaleAndExpires(e) {
    if (!Array.isArray(e)) return [e, e];
    switch (e.length) {
      case 0:
        return [0, 0];
      case 1:
        return [e[0], e[0]];
      default:
        return [e[0], e[1]];
    }
  }
  clearTimer(e) {
    let t = this.removalTimers.find((i) => this.paramsAreEqual(i.params, e));
    t && (clearTimeout(t.timer), this.removalTimers = this.removalTimers.filter((i) => i !== t));
  }
  scheduleForRemoval(e, t) {
    if (!(typeof window > "u") && (this.clearTimer(e), t > 0)) {
      let i = window.setTimeout(() => this.remove(e), t);
      this.removalTimers.push({ params: e, timer: i });
    }
  }
  get(e) {
    return this.findCached(e) || this.findInFlight(e);
  }
  use(e, t) {
    let i = `${t.url.pathname}-${Date.now()}-${Math.random().toString(36).substring(7)}`;
    return this.currentUseId = i, e.response.then((s) => {
      if (this.currentUseId === i) return s.mergeParams({ ...t, onPrefetched: () => {
      } }), this.removeSingleUseItems(t), s.handle();
    });
  }
  removeSingleUseItems(e) {
    this.cached = this.cached.filter((t) => this.paramsAreEqual(t.params, e) ? !t.singleUse : true);
  }
  findCached(e) {
    return this.cached.find((t) => this.paramsAreEqual(t.params, e)) || null;
  }
  findInFlight(e) {
    return this.inFlightRequests.find((t) => this.paramsAreEqual(t.params, e)) || null;
  }
  paramsAreEqual(e, t) {
    return ae(e, t, ["showProgress", "replace", "prefetch", "onBefore", "onStart", "onProgress", "onFinish", "onCancel", "onSuccess", "onError", "onPrefetched", "onCancelToken", "onPrefetching", "async"]);
  }
};
var x = new ce();
var j = class r {
  constructor(e) {
    this.callbacks = [];
    if (!e.prefetch) this.params = e;
    else {
      let t = { onBefore: this.wrapCallback(e, "onBefore"), onStart: this.wrapCallback(e, "onStart"), onProgress: this.wrapCallback(e, "onProgress"), onFinish: this.wrapCallback(e, "onFinish"), onCancel: this.wrapCallback(e, "onCancel"), onSuccess: this.wrapCallback(e, "onSuccess"), onError: this.wrapCallback(e, "onError"), onCancelToken: this.wrapCallback(e, "onCancelToken"), onPrefetched: this.wrapCallback(e, "onPrefetched"), onPrefetching: this.wrapCallback(e, "onPrefetching") };
      this.params = { ...e, ...t, onPrefetchResponse: e.onPrefetchResponse || (() => {
      }) };
    }
  }
  static create(e) {
    return new r(e);
  }
  data() {
    return this.params.method === "get" ? null : this.params.data;
  }
  queryParams() {
    return this.params.method === "get" ? this.params.data : {};
  }
  isPartial() {
    return this.params.only.length > 0 || this.params.except.length > 0 || this.params.reset.length > 0;
  }
  onCancelToken(e) {
    this.params.onCancelToken({ cancel: e });
  }
  markAsFinished() {
    this.params.completed = true, this.params.cancelled = false, this.params.interrupted = false;
  }
  markAsCancelled({ cancelled: e = true, interrupted: t = false }) {
    this.params.onCancel(), this.params.completed = false, this.params.cancelled = e, this.params.interrupted = t;
  }
  wasCancelledAtAll() {
    return this.params.cancelled || this.params.interrupted;
  }
  onFinish() {
    this.params.onFinish(this.params);
  }
  onStart() {
    this.params.onStart(this.params);
  }
  onPrefetching() {
    this.params.onPrefetching(this.params);
  }
  onPrefetchResponse(e) {
    this.params.onPrefetchResponse && this.params.onPrefetchResponse(e);
  }
  all() {
    return this.params;
  }
  headers() {
    let e = { ...this.params.headers };
    this.isPartial() && (e["X-Inertia-Partial-Component"] = n.get().component);
    let t = this.params.only.concat(this.params.reset);
    return t.length > 0 && (e["X-Inertia-Partial-Data"] = t.join(",")), this.params.except.length > 0 && (e["X-Inertia-Partial-Except"] = this.params.except.join(",")), this.params.reset.length > 0 && (e["X-Inertia-Reset"] = this.params.reset.join(",")), this.params.errorBag && this.params.errorBag.length > 0 && (e["X-Inertia-Error-Bag"] = this.params.errorBag), e;
  }
  setPreserveOptions(e) {
    this.params.preserveScroll = this.resolvePreserveOption(this.params.preserveScroll, e), this.params.preserveState = this.resolvePreserveOption(this.params.preserveState, e);
  }
  runCallbacks() {
    this.callbacks.forEach(({ name: e, args: t }) => {
      this.params[e](...t);
    });
  }
  merge(e) {
    this.params = { ...this.params, ...e };
  }
  wrapCallback(e, t) {
    return (...i) => {
      this.recordCallback(t, i), e[t](...i);
    };
  }
  recordCallback(e, t) {
    this.callbacks.push({ name: e, args: t });
  }
  resolvePreserveOption(e, t) {
    return typeof e == "function" ? e(t) : e === "errors" ? Object.keys(t.props.errors || {}).length > 0 : e;
  }
};
var Ve = { modal: null, listener: null, show(r4) {
  typeof r4 == "object" && (r4 = `All Inertia requests must receive a valid Inertia response, however a plain JSON response was received.<hr>${JSON.stringify(r4)}`);
  let e = document.createElement("html");
  e.innerHTML = r4, e.querySelectorAll("a").forEach((i) => i.setAttribute("target", "_top")), this.modal = document.createElement("div"), this.modal.style.position = "fixed", this.modal.style.width = "100vw", this.modal.style.height = "100vh", this.modal.style.padding = "50px", this.modal.style.boxSizing = "border-box", this.modal.style.backgroundColor = "rgba(0, 0, 0, .6)", this.modal.style.zIndex = 2e5, this.modal.addEventListener("click", () => this.hide());
  let t = document.createElement("iframe");
  if (t.style.backgroundColor = "white", t.style.borderRadius = "5px", t.style.width = "100%", t.style.height = "100%", this.modal.appendChild(t), document.body.prepend(this.modal), document.body.style.overflow = "hidden", !t.contentWindow) throw new Error("iframe not yet ready.");
  t.contentWindow.document.open(), t.contentWindow.document.write(e.outerHTML), t.contentWindow.document.close(), this.listener = this.hideOnEscape.bind(this), document.addEventListener("keydown", this.listener);
}, hide() {
  this.modal.outerHTML = "", this.modal = null, document.body.style.overflow = "visible", document.removeEventListener("keydown", this.listener);
}, hideOnEscape(r4) {
  r4.keyCode === 27 && this.hide();
} };
var Qe = new A();
var U = class r2 {
  constructor(e, t, i) {
    this.requestParams = e;
    this.response = t;
    this.originatingPage = i;
  }
  static create(e, t, i) {
    return new r2(e, t, i);
  }
  async handlePrefetch() {
    L(this.requestParams.all().url, window.location) && this.handle();
  }
  async handle() {
    return Qe.add(() => this.process());
  }
  async process() {
    if (this.requestParams.all().prefetch) return this.requestParams.all().prefetch = false, this.requestParams.all().onPrefetched(this.response, this.requestParams.all()), be(this.response, this.requestParams.all()), Promise.resolve();
    if (this.requestParams.runCallbacks(), !this.isInertiaResponse()) return this.handleNonInertiaResponse();
    await a.processQueue(), a.preserveUrl = this.requestParams.all().preserveUrl, await this.setPage();
    let e = n.get().props.errors || {};
    if (Object.keys(e).length > 0) {
      let t = this.getScopedErrors(e);
      return pe(t), this.requestParams.all().onError(t);
    }
    ve(n.get()), await this.requestParams.all().onSuccess(n.get()), a.preserveUrl = false;
  }
  mergeParams(e) {
    this.requestParams.merge(e);
  }
  async handleNonInertiaResponse() {
    if (this.isLocationVisit()) {
      let t = y(this.getHeader("x-inertia-location"));
      return te(this.requestParams.all().url, t), this.locationVisit(t);
    }
    let e = { ...this.response, data: this.getDataFromResponse(this.response.data) };
    if (me(e)) return Ve.show(e.data);
  }
  isInertiaResponse() {
    return this.hasHeader("x-inertia");
  }
  hasStatus(e) {
    return this.response.status === e;
  }
  getHeader(e) {
    return this.response.headers[e];
  }
  hasHeader(e) {
    return this.getHeader(e) !== void 0;
  }
  isLocationVisit() {
    return this.hasStatus(409) && this.hasHeader("x-inertia-location");
  }
  locationVisit(e) {
    try {
      if (m.set(m.locationVisitKey, { preserveScroll: this.requestParams.all().preserveScroll === true }), typeof window > "u") return;
      L(window.location, e) ? window.location.reload() : window.location.href = e.href;
    } catch {
      return false;
    }
  }
  async setPage() {
    let e = this.getDataFromResponse(this.response.data);
    return this.shouldSetPage(e) ? (this.mergeProps(e), await this.setRememberedState(e), this.requestParams.setPreserveOptions(e), e.url = a.preserveUrl ? n.get().url : this.pageUrl(e), n.set(e, { replace: this.requestParams.all().replace, preserveScroll: this.requestParams.all().preserveScroll, preserveState: this.requestParams.all().preserveState })) : Promise.resolve();
  }
  getDataFromResponse(e) {
    if (typeof e != "string") return e;
    try {
      return JSON.parse(e);
    } catch {
      return e;
    }
  }
  shouldSetPage(e) {
    if (!this.requestParams.all().async || this.originatingPage.component !== e.component) return true;
    if (this.originatingPage.component !== n.get().component) return false;
    let t = y(this.originatingPage.url), i = y(n.get().url);
    return t.origin === i.origin && t.pathname === i.pathname;
  }
  pageUrl(e) {
    let t = y(e.url);
    return te(this.requestParams.all().url, t), t.pathname + t.search + t.hash;
  }
  mergeProps(e) {
    if (!this.requestParams.isPartial() || e.component !== n.get().component) return;
    let t = e.mergeProps || [], i = e.deepMergeProps || [];
    t.forEach((s) => {
      let o = e.props[s];
      Array.isArray(o) ? e.props[s] = [...n.get().props[s] || [], ...o] : typeof o == "object" && o !== null && (e.props[s] = { ...n.get().props[s] || [], ...o });
    }), i.forEach((s) => {
      let o = e.props[s], u = n.get().props[s], d = (p, c) => Array.isArray(c) ? [...Array.isArray(p) ? p : [], ...c] : typeof c == "object" && c !== null ? Object.keys(c).reduce((l, h) => (l[h] = d(p ? p[h] : void 0, c[h]), l), { ...p }) : c;
      e.props[s] = d(u, o);
    }), e.props = { ...n.get().props, ...e.props };
  }
  async setRememberedState(e) {
    let t = await a.getState(a.rememberedState, {});
    this.requestParams.all().preserveState && t && e.component === n.get().component && (e.rememberedState = t);
  }
  getScopedErrors(e) {
    return this.requestParams.all().errorBag ? e[this.requestParams.all().errorBag || ""] || {} : e;
  }
};
var D = class r3 {
  constructor(e, t) {
    this.page = t;
    this.requestHasFinished = false;
    this.requestParams = j.create(e), this.cancelToken = new AbortController();
  }
  static create(e, t) {
    return new r3(e, t);
  }
  async send() {
    this.requestParams.onCancelToken(() => this.cancel({ cancelled: true })), ge(this.requestParams.all()), this.requestParams.onStart(), this.requestParams.all().prefetch && (this.requestParams.onPrefetching(), ye(this.requestParams.all()));
    let e = this.requestParams.all().prefetch;
    return axios_default({ method: this.requestParams.all().method, url: F(this.requestParams.all().url).href, data: this.requestParams.data(), params: this.requestParams.queryParams(), signal: this.cancelToken.signal, headers: this.getHeaders(), onUploadProgress: this.onProgress.bind(this), responseType: "text" }).then((t) => (this.response = U.create(this.requestParams, t, this.page), this.response.handle())).catch((t) => (t == null ? void 0 : t.response) ? (this.response = U.create(this.requestParams, t.response, this.page), this.response.handle()) : Promise.reject(t)).catch((t) => {
      if (!axios_default.isCancel(t) && de(t)) return Promise.reject(t);
    }).finally(() => {
      this.finish(), e && this.response && this.requestParams.onPrefetchResponse(this.response);
    });
  }
  finish() {
    this.requestParams.wasCancelledAtAll() || (this.requestParams.markAsFinished(), this.fireFinishEvents());
  }
  fireFinishEvents() {
    this.requestHasFinished || (this.requestHasFinished = true, he(this.requestParams.all()), this.requestParams.onFinish());
  }
  cancel({ cancelled: e = false, interrupted: t = false }) {
    this.requestHasFinished || (this.cancelToken.abort(), this.requestParams.markAsCancelled({ cancelled: e, interrupted: t }), this.fireFinishEvents());
  }
  onProgress(e) {
    this.requestParams.data() instanceof FormData && (e.percentage = e.progress ? Math.round(e.progress * 100) : 0, fe(e), this.requestParams.all().onProgress(e));
  }
  getHeaders() {
    let e = { ...this.requestParams.headers(), Accept: "text/html, application/xhtml+xml", "X-Requested-With": "XMLHttpRequest", "X-Inertia": true };
    return n.get().version && (e["X-Inertia-Version"] = n.get().version), e;
  }
};
var H = class {
  constructor({ maxConcurrent: e, interruptible: t }) {
    this.requests = [];
    this.maxConcurrent = e, this.interruptible = t;
  }
  send(e) {
    this.requests.push(e), e.send().then(() => {
      this.requests = this.requests.filter((t) => t !== e);
    });
  }
  interruptInFlight() {
    this.cancel({ interrupted: true }, false);
  }
  cancelInFlight() {
    this.cancel({ cancelled: true }, true);
  }
  cancel({ cancelled: e = false, interrupted: t = false } = {}, i) {
    var _a;
    if (!this.shouldCancel(i)) return;
    (_a = this.requests.shift()) == null ? void 0 : _a.cancel({ interrupted: t, cancelled: e });
  }
  shouldCancel(e) {
    return e ? true : this.interruptible && this.requests.length >= this.maxConcurrent;
  }
};
var W = class {
  constructor() {
    this.syncRequestStream = new H({ maxConcurrent: 1, interruptible: true });
    this.asyncRequestStream = new H({ maxConcurrent: 1 / 0, interruptible: false });
  }
  init({ initialPage: e, resolveComponent: t, swapComponent: i }) {
    n.init({ initialPage: e, resolveComponent: t, swapComponent: i }), G.handle(), E.init(), E.on("missingHistoryItem", () => {
      typeof window < "u" && this.visit(window.location.href, { preserveState: true, preserveScroll: true, replace: true });
    }), E.on("loadDeferredProps", () => {
      this.loadDeferredProps();
    });
  }
  get(e, t = {}, i = {}) {
    return this.visit(e, { ...i, method: "get", data: t });
  }
  post(e, t = {}, i = {}) {
    return this.visit(e, { preserveState: true, ...i, method: "post", data: t });
  }
  put(e, t = {}, i = {}) {
    return this.visit(e, { preserveState: true, ...i, method: "put", data: t });
  }
  patch(e, t = {}, i = {}) {
    return this.visit(e, { preserveState: true, ...i, method: "patch", data: t });
  }
  delete(e, t = {}) {
    return this.visit(e, { preserveState: true, ...t, method: "delete" });
  }
  reload(e = {}) {
    if (!(typeof window > "u")) return this.visit(window.location.href, { ...e, preserveScroll: true, preserveState: true, async: true, headers: { ...e.headers || {}, "Cache-Control": "no-cache" } });
  }
  remember(e, t = "default") {
    a.remember(e, t);
  }
  restore(e = "default") {
    return a.restore(e);
  }
  on(e, t) {
    return typeof window > "u" ? () => {
    } : E.onGlobalEvent(e, t);
  }
  cancel() {
    this.syncRequestStream.cancelInFlight();
  }
  cancelAll() {
    this.asyncRequestStream.cancelInFlight(), this.syncRequestStream.cancelInFlight();
  }
  poll(e, t = {}, i = {}) {
    return Ae.add(e, () => this.reload(t), { autoStart: i.autoStart ?? true, keepAlive: i.keepAlive ?? false });
  }
  visit(e, t = {}) {
    let i = this.getPendingVisit(e, { ...t, showProgress: t.showProgress ?? !t.async }), s = this.getVisitEvents(t);
    if (s.onBefore(i) === false || !Y(i)) return;
    let o = i.async ? this.asyncRequestStream : this.syncRequestStream;
    o.interruptInFlight(), !n.isCleared() && !i.preserveUrl && v.save();
    let u = { ...i, ...s }, d = x.get(u);
    d ? (X(d.inFlight), x.use(d, u)) : (X(true), o.send(D.create(u, n.get())));
  }
  getCached(e, t = {}) {
    return x.findCached(this.getPrefetchParams(e, t));
  }
  flush(e, t = {}) {
    x.remove(this.getPrefetchParams(e, t));
  }
  flushAll() {
    x.removeAll();
  }
  getPrefetching(e, t = {}) {
    return x.findInFlight(this.getPrefetchParams(e, t));
  }
  prefetch(e, t = {}, { cacheFor: i = 3e4 }) {
    if (t.method !== "get") throw new Error("Prefetch requests must use the GET method");
    let s = this.getPendingVisit(e, { ...t, async: true, showProgress: false, prefetch: true }), o = s.url.origin + s.url.pathname + s.url.search, u = window.location.origin + window.location.pathname + window.location.search;
    if (o === u) return;
    let d = this.getVisitEvents(t);
    if (d.onBefore(s) === false || !Y(s)) return;
    J(), this.asyncRequestStream.interruptInFlight();
    let p = { ...s, ...d };
    new Promise((l) => {
      let h = () => {
        n.get() ? l() : setTimeout(h, 50);
      };
      h();
    }).then(() => {
      x.add(p, (l) => {
        this.asyncRequestStream.send(D.create(l, n.get()));
      }, { cacheFor: i });
    });
  }
  clearHistory() {
    a.clear();
  }
  decryptHistory() {
    return a.decrypt();
  }
  replace(e) {
    this.clientVisit(e, { replace: true });
  }
  push(e) {
    this.clientVisit(e);
  }
  clientVisit(e, { replace: t = false } = {}) {
    let i = n.get(), s = typeof e.props == "function" ? e.props(i.props) : e.props ?? i.props;
    n.set({ ...i, ...e, props: s }, { replace: t, preserveScroll: e.preserveScroll, preserveState: e.preserveState });
  }
  getPrefetchParams(e, t) {
    return { ...this.getPendingVisit(e, { ...t, async: true, showProgress: false, prefetch: true }), ...this.getVisitEvents(t) };
  }
  getPendingVisit(e, t, i = {}) {
    let s = { method: "get", data: {}, replace: false, preserveScroll: false, preserveState: false, only: [], except: [], headers: {}, errorBag: "", forceFormData: false, queryStringArrayFormat: "brackets", async: false, showProgress: true, fresh: false, reset: [], preserveUrl: false, prefetch: false, ...t }, [o, u] = xe(e, s.data, s.method, s.forceFormData, s.queryStringArrayFormat);
    return { cancelled: false, completed: false, interrupted: false, ...s, ...i, url: o, data: u };
  }
  getVisitEvents(e) {
    return { onCancelToken: e.onCancelToken || (() => {
    }), onBefore: e.onBefore || (() => {
    }), onStart: e.onStart || (() => {
    }), onProgress: e.onProgress || (() => {
    }), onFinish: e.onFinish || (() => {
    }), onCancel: e.onCancel || (() => {
    }), onSuccess: e.onSuccess || (() => {
    }), onError: e.onError || (() => {
    }), onPrefetched: e.onPrefetched || (() => {
    }), onPrefetching: e.onPrefetching || (() => {
    }) };
  }
  loadDeferredProps() {
    var _a;
    let e = (_a = n.get()) == null ? void 0 : _a.deferredProps;
    e && Object.entries(e).forEach(([t, i]) => {
      this.reload({ only: i });
    });
  }
};
var ze = { buildDOMElement(r4) {
  let e = document.createElement("template");
  e.innerHTML = r4;
  let t = e.content.firstChild;
  if (!r4.startsWith("<script ")) return t;
  let i = document.createElement("script");
  return i.innerHTML = t.innerHTML, t.getAttributeNames().forEach((s) => {
    i.setAttribute(s, t.getAttribute(s) || "");
  }), i;
}, isInertiaManagedElement(r4) {
  return r4.nodeType === Node.ELEMENT_NODE && r4.getAttribute("inertia") !== null;
}, findMatchingElementIndex(r4, e) {
  let t = r4.getAttribute("inertia");
  return t !== null ? e.findIndex((i) => i.getAttribute("inertia") === t) : -1;
}, update: V(function(r4) {
  let e = r4.map((i) => this.buildDOMElement(i));
  Array.from(document.head.childNodes).filter((i) => this.isInertiaManagedElement(i)).forEach((i) => {
    var _a, _b;
    let s = this.findMatchingElementIndex(i, e);
    if (s === -1) {
      (_a = i == null ? void 0 : i.parentNode) == null ? void 0 : _a.removeChild(i);
      return;
    }
    let o = e.splice(s, 1)[0];
    o && !i.isEqualNode(o) && ((_b = i == null ? void 0 : i.parentNode) == null ? void 0 : _b.replaceChild(o, i));
  }), e.forEach((i) => document.head.appendChild(i));
}, 1) };
function Ie(r4, e, t) {
  let i = {}, s = 0;
  function o() {
    let l = s += 1;
    return i[l] = [], l.toString();
  }
  function u(l) {
    l === null || Object.keys(i).indexOf(l) === -1 || (delete i[l], c());
  }
  function d(l, h = []) {
    l !== null && Object.keys(i).indexOf(l) > -1 && (i[l] = h), c();
  }
  function p() {
    let l = e(""), h = { ...l ? { title: `<title inertia="">${l}</title>` } : {} }, I = Object.values(i).reduce((w, R) => w.concat(R), []).reduce((w, R) => {
      if (R.indexOf("<") === -1) return w;
      if (R.indexOf("<title ") === 0) {
        let M = R.match(/(<title [^>]+>)(.*?)(<\/title>)/);
        return w.title = M ? `${M[1]}${e(M[2])}${M[3]}` : R, w;
      }
      let ue = R.match(/ inertia="[^"]+"/);
      return ue ? w[ue[0]] = R : w[Object.keys(w).length] = R, w;
    }, h);
    return Object.values(I);
  }
  function c() {
    r4 ? t(p()) : ze.update(p());
  }
  return c(), { forceUpdate: c, createProvider: function() {
    let l = o();
    return { update: (h) => d(l, h), disconnect: () => u(l) };
  } };
}
var f = "nprogress";
var P;
var g = { minimum: 0.08, easing: "linear", positionUsing: "translate3d", speed: 200, trickle: true, trickleSpeed: 200, showSpinner: true, barSelector: '[role="bar"]', spinnerSelector: '[role="spinner"]', parent: "body", color: "#29d", includeCSS: true, template: ['<div class="bar" role="bar">', '<div class="peg"></div>', "</div>", '<div class="spinner" role="spinner">', '<div class="spinner-icon"></div>', "</div>"].join("") };
var q = null;
var _e = (r4) => {
  Object.assign(g, r4), g.includeCSS && it(g.color), P = document.createElement("div"), P.id = f, P.innerHTML = g.template;
};
var z = (r4) => {
  let e = Le();
  r4 = He(r4, g.minimum, 1), q = r4 === 1 ? null : r4;
  let t = Ze(!e), i = t.querySelector(g.barSelector), s = g.speed, o = g.easing;
  t.offsetWidth, rt((u) => {
    let d = g.positionUsing === "translate3d" ? { transition: `all ${s}ms ${o}`, transform: `translate3d(${Q(r4)}%,0,0)` } : g.positionUsing === "translate" ? { transition: `all ${s}ms ${o}`, transform: `translate(${Q(r4)}%,0)` } : { marginLeft: `${Q(r4)}%` };
    for (let p in d) i.style[p] = d[p];
    if (r4 !== 1) return setTimeout(u, s);
    t.style.transition = "none", t.style.opacity = "1", t.offsetWidth, setTimeout(() => {
      t.style.transition = `all ${s}ms linear`, t.style.opacity = "0", setTimeout(() => {
        De(), t.style.transition = "", t.style.opacity = "", u();
      }, s);
    }, s);
  });
};
var Le = () => typeof q == "number";
var ke = () => {
  q || z(0);
  let r4 = function() {
    setTimeout(function() {
      q && (Oe(), r4());
    }, g.trickleSpeed);
  };
  g.trickle && r4();
};
var Ye = (r4) => {
  !r4 && !q || (Oe(0.3 + 0.5 * Math.random()), z(1));
};
var Oe = (r4) => {
  let e = q;
  if (e === null) return ke();
  if (!(e > 1)) return r4 = typeof r4 == "number" ? r4 : (() => {
    let t = { 0.1: [0, 0.2], 0.04: [0.2, 0.5], 0.02: [0.5, 0.8], 5e-3: [0.8, 0.99] };
    for (let i in t) if (e >= t[i][0] && e < t[i][1]) return parseFloat(i);
    return 0;
  })(), z(He(e + r4, 0, 0.994));
};
var Ze = (r4) => {
  var _a;
  if (et()) return document.getElementById(f);
  document.documentElement.classList.add(`${f}-busy`);
  let e = P.querySelector(g.barSelector), t = r4 ? "-100" : Q(q || 0), i = Ue();
  return e.style.transition = "all 0 linear", e.style.transform = `translate3d(${t}%,0,0)`, g.showSpinner || ((_a = P.querySelector(g.spinnerSelector)) == null ? void 0 : _a.remove()), i !== document.body && i.classList.add(`${f}-custom-parent`), i.appendChild(P), P;
};
var Ue = () => tt(g.parent) ? g.parent : document.querySelector(g.parent);
var De = () => {
  document.documentElement.classList.remove(`${f}-busy`), Ue().classList.remove(`${f}-custom-parent`), P == null ? void 0 : P.remove();
};
var et = () => document.getElementById(f) !== null;
var tt = (r4) => typeof HTMLElement == "object" ? r4 instanceof HTMLElement : r4 && typeof r4 == "object" && r4.nodeType === 1 && typeof r4.nodeName == "string";
function He(r4, e, t) {
  return r4 < e ? e : r4 > t ? t : r4;
}
var Q = (r4) => (-1 + r4) * 100;
var rt = /* @__PURE__ */ (() => {
  let r4 = [], e = () => {
    let t = r4.shift();
    t && t(e);
  };
  return (t) => {
    r4.push(t), r4.length === 1 && e();
  };
})();
var it = (r4) => {
  let e = document.createElement("style");
  e.textContent = `
    #${f} {
      pointer-events: none;
    }

    #${f} .bar {
      background: ${r4};

      position: fixed;
      z-index: 1031;
      top: 0;
      left: 0;

      width: 100%;
      height: 2px;
    }

    #${f} .peg {
      display: block;
      position: absolute;
      right: 0px;
      width: 100px;
      height: 100%;
      box-shadow: 0 0 10px ${r4}, 0 0 5px ${r4};
      opacity: 1.0;

      transform: rotate(3deg) translate(0px, -4px);
    }

    #${f} .spinner {
      display: block;
      position: fixed;
      z-index: 1031;
      top: 15px;
      right: 15px;
    }

    #${f} .spinner-icon {
      width: 18px;
      height: 18px;
      box-sizing: border-box;

      border: solid 2px transparent;
      border-top-color: ${r4};
      border-left-color: ${r4};
      border-radius: 50%;

      animation: ${f}-spinner 400ms linear infinite;
    }

    .${f}-custom-parent {
      overflow: hidden;
      position: relative;
    }

    .${f}-custom-parent #${f} .spinner,
    .${f}-custom-parent #${f} .bar {
      position: absolute;
    }

    @keyframes ${f}-spinner {
      0%   { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  `, document.head.appendChild(e);
};
var st = () => {
  P && (P.style.display = "");
};
var nt = () => {
  P && (P.style.display = "none");
};
var b = { configure: _e, isStarted: Le, done: Ye, set: z, remove: De, start: ke, status: q, show: st, hide: nt };
var _ = 0;
var X = (r4 = false) => {
  _ = Math.max(0, _ - 1), (r4 || _ === 0) && b.show();
};
var J = () => {
  _++, b.hide();
};
function ot(r4) {
  document.addEventListener("inertia:start", (e) => at2(e, r4)), document.addEventListener("inertia:progress", lt);
}
function at2(r4, e) {
  r4.detail.visit.showProgress || J();
  let t = setTimeout(() => b.start(), e);
  document.addEventListener("inertia:finish", (i) => ct(i, t), { once: true });
}
function lt(r4) {
  var _a;
  b.isStarted() && ((_a = r4.detail.progress) == null ? void 0 : _a.percentage) && b.set(Math.max(b.status, r4.detail.progress.percentage / 100 * 0.9));
}
function ct(r4, e) {
  clearTimeout(e), b.isStarted() && (r4.detail.visit.completed ? b.done() : r4.detail.visit.interrupted ? b.set(0) : r4.detail.visit.cancelled && (b.done(), b.remove()));
}
function Me({ delay: r4 = 250, color: e = "#29d", includeCSS: t = true, showSpinner: i = false } = {}) {
  ot(r4), b.configure({ showSpinner: i, includeCSS: t, color: e });
}
function Ne(r4) {
  let e = r4.currentTarget.tagName.toLowerCase() === "a";
  return !(r4.target && (r4 == null ? void 0 : r4.target).isContentEditable || r4.defaultPrevented || e && r4.altKey || e && r4.ctrlKey || e && r4.metaKey || e && r4.shiftKey || e && "button" in r4 && r4.button !== 0);
}
var Wr = new W();

// node_modules/@inertiajs/react/dist/index.esm.js
var import_react = __toESM(require_react());
var import_react2 = __toESM(require_react());
var import_react3 = __toESM(require_react());
var import_react4 = __toESM(require_react());
var import_react5 = __toESM(require_react());
var import_react6 = __toESM(require_react());
var import_react7 = __toESM(require_react());
var import_react8 = __toESM(require_react());

// node_modules/es-toolkit/dist/compat/array/castArray.mjs
function castArray(value) {
  if (arguments.length === 0) {
    return [];
  }
  return Array.isArray(value) ? value : [value];
}

// node_modules/es-toolkit/dist/compat/_internal/toArray.mjs
function toArray(value) {
  return Array.isArray(value) ? value : Array.from(value);
}

// node_modules/es-toolkit/dist/compat/predicate/isArrayLike.mjs
function isArrayLike(value) {
  return value != null && typeof value !== "function" && isLength(value.length);
}

// node_modules/es-toolkit/dist/compat/array/chunk.mjs
function chunk2(arr, size2 = 1) {
  size2 = Math.max(Math.floor(size2), 0);
  if (size2 === 0 || !isArrayLike(arr)) {
    return [];
  }
  return chunk(toArray(arr), size2);
}

// node_modules/es-toolkit/dist/compat/array/compact.mjs
function compact2(arr) {
  if (!isArrayLike(arr)) {
    return [];
  }
  return compact(Array.from(arr));
}

// node_modules/es-toolkit/dist/compat/array/concat.mjs
function concat(...values2) {
  return flatten(values2);
}

// node_modules/es-toolkit/dist/compat/predicate/isArrayLikeObject.mjs
function isArrayLikeObject(value) {
  return isObjectLike(value) && isArrayLike(value);
}

// node_modules/es-toolkit/dist/compat/array/difference.mjs
function difference2(arr, ...values2) {
  if (!isArrayLikeObject(arr)) {
    return [];
  }
  const arr1 = toArray(arr);
  const arr2 = [];
  for (let i = 0; i < values2.length; i++) {
    const value = values2[i];
    if (isArrayLikeObject(value)) {
      arr2.push(...Array.from(value));
    }
  }
  return difference(arr1, arr2);
}

// node_modules/es-toolkit/dist/compat/array/last.mjs
function last2(array) {
  if (!isArrayLike(array)) {
    return void 0;
  }
  return last(toArray(array));
}

// node_modules/es-toolkit/dist/compat/_internal/flattenArrayLike.mjs
function flattenArrayLike(values2) {
  const result2 = [];
  for (let i = 0; i < values2.length; i++) {
    const arrayLike = values2[i];
    if (!isArrayLikeObject(arrayLike)) {
      continue;
    }
    for (let j2 = 0; j2 < arrayLike.length; j2++) {
      result2.push(arrayLike[j2]);
    }
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/_internal/isDeepKey.mjs
function isDeepKey(key) {
  switch (typeof key) {
    case "number":
    case "symbol": {
      return false;
    }
    case "string": {
      return key.includes(".") || key.includes("[") || key.includes("]");
    }
  }
}

// node_modules/es-toolkit/dist/compat/_internal/toKey.mjs
function toKey(value) {
  var _a;
  if (typeof value === "string" || typeof value === "symbol") {
    return value;
  }
  if (Object.is((_a = value == null ? void 0 : value.valueOf) == null ? void 0 : _a.call(value), -0)) {
    return "-0";
  }
  return String(value);
}

// node_modules/es-toolkit/dist/compat/util/toPath.mjs
function toPath(deepKey) {
  const result2 = [];
  const length = deepKey.length;
  if (length === 0) {
    return result2;
  }
  let index = 0;
  let key = "";
  let quoteChar = "";
  let bracket = false;
  if (deepKey.charCodeAt(0) === 46) {
    result2.push("");
    index++;
  }
  while (index < length) {
    const char = deepKey[index];
    if (quoteChar) {
      if (char === "\\" && index + 1 < length) {
        index++;
        key += deepKey[index];
      } else if (char === quoteChar) {
        quoteChar = "";
      } else {
        key += char;
      }
    } else if (bracket) {
      if (char === '"' || char === "'") {
        quoteChar = char;
      } else if (char === "]") {
        bracket = false;
        result2.push(key);
        key = "";
      } else {
        key += char;
      }
    } else {
      if (char === "[") {
        bracket = true;
        if (key) {
          result2.push(key);
          key = "";
        }
      } else if (char === ".") {
        if (key) {
          result2.push(key);
          key = "";
        }
      } else {
        key += char;
      }
    }
    index++;
  }
  if (key) {
    result2.push(key);
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/object/get.mjs
function get(object, path, defaultValue) {
  if (object == null) {
    return defaultValue;
  }
  switch (typeof path) {
    case "string": {
      const result2 = object[path];
      if (result2 === void 0) {
        if (isDeepKey(path)) {
          return get(object, toPath(path), defaultValue);
        } else {
          return defaultValue;
        }
      }
      return result2;
    }
    case "number":
    case "symbol": {
      if (typeof path === "number") {
        path = toKey(path);
      }
      const result2 = object[path];
      if (result2 === void 0) {
        return defaultValue;
      }
      return result2;
    }
    default: {
      if (Array.isArray(path)) {
        return getWithPath(object, path, defaultValue);
      }
      if (Object.is(path == null ? void 0 : path.valueOf(), -0)) {
        path = "-0";
      } else {
        path = String(path);
      }
      const result2 = object[path];
      if (result2 === void 0) {
        return defaultValue;
      }
      return result2;
    }
  }
}
function getWithPath(object, path, defaultValue) {
  if (path.length === 0) {
    return defaultValue;
  }
  let current = object;
  for (let index = 0; index < path.length; index++) {
    if (current == null) {
      return defaultValue;
    }
    current = current[path[index]];
  }
  if (current === void 0) {
    return defaultValue;
  }
  return current;
}

// node_modules/es-toolkit/dist/compat/object/property.mjs
function property(path) {
  return function(object) {
    return get(object, path);
  };
}

// node_modules/es-toolkit/dist/compat/predicate/isObject.mjs
function isObject(value) {
  return value !== null && (typeof value === "object" || typeof value === "function");
}

// node_modules/es-toolkit/dist/compat/predicate/isMatch.mjs
function isMatch(target, source) {
  if (source === target) {
    return true;
  }
  switch (typeof source) {
    case "object": {
      if (source == null) {
        return true;
      }
      const keys2 = Object.keys(source);
      if (target == null) {
        return keys2.length === 0;
      }
      if (Array.isArray(source)) {
        return isArrayMatch(target, source);
      }
      if (source instanceof Map) {
        return isMapMatch(target, source);
      }
      if (source instanceof Set) {
        return isSetMatch(target, source);
      }
      for (let i = 0; i < keys2.length; i++) {
        const key = keys2[i];
        if (!isPrimitive(target) && !(key in target)) {
          return false;
        }
        if (source[key] === void 0 && target[key] !== void 0) {
          return false;
        }
        if (source[key] === null && target[key] !== null) {
          return false;
        }
        if (!isMatch(target[key], source[key])) {
          return false;
        }
      }
      return true;
    }
    case "function": {
      if (Object.keys(source).length > 0) {
        return isMatch(target, { ...source });
      }
      return false;
    }
    default: {
      if (!isObject(target)) {
        return eq(target, source);
      }
      return !source;
    }
  }
}
function isMapMatch(target, source) {
  if (source.size === 0) {
    return true;
  }
  if (!(target instanceof Map)) {
    return false;
  }
  for (const [key, value] of source.entries()) {
    if (!isMatch(target.get(key), value)) {
      return false;
    }
  }
  return true;
}
function isArrayMatch(target, source) {
  if (source.length === 0) {
    return true;
  }
  if (!Array.isArray(target)) {
    return false;
  }
  const countedIndex = /* @__PURE__ */ new Set();
  for (let i = 0; i < source.length; i++) {
    const sourceItem = source[i];
    const index = target.findIndex((targetItem, index2) => {
      return isMatch(targetItem, sourceItem) && !countedIndex.has(index2);
    });
    if (index === -1) {
      return false;
    }
    countedIndex.add(index);
  }
  return true;
}
function isSetMatch(target, source) {
  if (source.size === 0) {
    return true;
  }
  if (!(target instanceof Set)) {
    return false;
  }
  return isArrayMatch([...target], [...source]);
}

// node_modules/es-toolkit/dist/compat/predicate/matches.mjs
function matches(source) {
  source = cloneDeep(source);
  return (target) => {
    return isMatch(target, source);
  };
}

// node_modules/es-toolkit/dist/compat/object/cloneDeepWith.mjs
function cloneDeepWith2(obj, cloneValue) {
  return cloneDeepWith(obj, (value, key, object, stack) => {
    const cloned = cloneValue == null ? void 0 : cloneValue(value, key, object, stack);
    if (cloned != null) {
      return cloned;
    }
    if (typeof obj !== "object") {
      return void 0;
    }
    switch (Object.prototype.toString.call(obj)) {
      case numberTag:
      case stringTag:
      case booleanTag: {
        const result2 = new obj.constructor(obj == null ? void 0 : obj.valueOf());
        copyProperties(result2, obj);
        return result2;
      }
      case argumentsTag: {
        const result2 = {};
        copyProperties(result2, obj);
        result2.length = obj.length;
        result2[Symbol.iterator] = obj[Symbol.iterator];
        return result2;
      }
      default: {
        return void 0;
      }
    }
  });
}

// node_modules/es-toolkit/dist/compat/object/cloneDeep.mjs
function cloneDeep2(obj) {
  return cloneDeepWith2(obj);
}

// node_modules/es-toolkit/dist/compat/_internal/isIndex.mjs
var IS_UNSIGNED_INTEGER = /^(?:0|[1-9]\d*)$/;
function isIndex(value, length = Number.MAX_SAFE_INTEGER) {
  switch (typeof value) {
    case "number": {
      return Number.isInteger(value) && value >= 0 && value < length;
    }
    case "symbol": {
      return false;
    }
    case "string": {
      return IS_UNSIGNED_INTEGER.test(value);
    }
  }
}

// node_modules/es-toolkit/dist/compat/predicate/isArguments.mjs
function isArguments(value) {
  return value !== null && typeof value === "object" && getTag(value) === "[object Arguments]";
}

// node_modules/es-toolkit/dist/compat/object/has.mjs
function has(object, path) {
  let resolvedPath;
  if (Array.isArray(path)) {
    resolvedPath = path;
  } else if (typeof path === "string" && isDeepKey(path) && (object == null ? void 0 : object[path]) == null) {
    resolvedPath = toPath(path);
  } else {
    resolvedPath = [path];
  }
  if (resolvedPath.length === 0) {
    return false;
  }
  let current = object;
  for (let i = 0; i < resolvedPath.length; i++) {
    const key = resolvedPath[i];
    if (current == null || !Object.hasOwn(current, key)) {
      const isSparseIndex = (Array.isArray(current) || isArguments(current)) && isIndex(key) && key < current.length;
      if (!isSparseIndex) {
        return false;
      }
    }
    current = current[key];
  }
  return true;
}

// node_modules/es-toolkit/dist/compat/predicate/matchesProperty.mjs
function matchesProperty(property2, source) {
  switch (typeof property2) {
    case "object": {
      if (Object.is(property2 == null ? void 0 : property2.valueOf(), -0)) {
        property2 = "-0";
      }
      break;
    }
    case "number": {
      property2 = toKey(property2);
      break;
    }
  }
  source = cloneDeep2(source);
  return function(target) {
    const result2 = get(target, property2);
    if (result2 === void 0) {
      return has(target, property2);
    }
    if (source === void 0) {
      return result2 === void 0;
    }
    return isMatch(result2, source);
  };
}

// node_modules/es-toolkit/dist/compat/util/iteratee.mjs
function iteratee(value) {
  if (value == null) {
    return identity;
  }
  switch (typeof value) {
    case "function": {
      return value;
    }
    case "object": {
      if (Array.isArray(value) && value.length === 2) {
        return matchesProperty(value[0], value[1]);
      }
      return matches(value);
    }
    case "string":
    case "symbol":
    case "number": {
      return property(value);
    }
  }
}

// node_modules/es-toolkit/dist/compat/array/differenceBy.mjs
function differenceBy2(arr, ..._values) {
  if (!isArrayLikeObject(arr)) {
    return [];
  }
  const iteratee$1 = last2(_values);
  const values2 = flattenArrayLike(_values);
  if (isArrayLikeObject(iteratee$1)) {
    return difference(Array.from(arr), values2);
  }
  return differenceBy(Array.from(arr), values2, iteratee(iteratee$1));
}

// node_modules/es-toolkit/dist/compat/array/differenceWith.mjs
function differenceWith2(array, ...values2) {
  if (!isArrayLikeObject(array)) {
    return [];
  }
  const comparator = last2(values2);
  const flattenedValues = flattenArrayLike(values2);
  if (typeof comparator === "function") {
    return differenceWith(Array.from(array), flattenedValues, comparator);
  }
  return difference(Array.from(array), flattenedValues);
}

// node_modules/es-toolkit/dist/compat/array/drop.mjs
function drop2(collection, itemsCount = 1, guard) {
  if (!isArrayLike(collection)) {
    return [];
  }
  itemsCount = guard ? 1 : toInteger(itemsCount);
  return drop(toArray(collection), itemsCount);
}

// node_modules/es-toolkit/dist/compat/array/dropRight.mjs
function dropRight2(collection, itemsCount = 1, guard) {
  if (!isArrayLike(collection)) {
    return [];
  }
  itemsCount = guard ? 1 : toInteger(itemsCount);
  return dropRight(toArray(collection), itemsCount);
}

// node_modules/es-toolkit/dist/compat/array/dropRightWhile.mjs
function dropRightWhile2(arr, predicate) {
  if (!isArrayLike(arr)) {
    return [];
  }
  return dropRightWhileImpl(Array.from(arr), predicate);
}
function dropRightWhileImpl(arr, predicate) {
  switch (typeof predicate) {
    case "function": {
      return dropRightWhile(arr, (item, index, arr2) => Boolean(predicate(item, index, arr2)));
    }
    case "object": {
      if (Array.isArray(predicate) && predicate.length === 2) {
        const key = predicate[0];
        const value = predicate[1];
        return dropRightWhile(arr, matchesProperty(key, value));
      } else {
        return dropRightWhile(arr, matches(predicate));
      }
    }
    case "symbol":
    case "number":
    case "string": {
      return dropRightWhile(arr, property(predicate));
    }
  }
}

// node_modules/es-toolkit/dist/compat/array/dropWhile.mjs
function dropWhile2(arr, predicate) {
  if (!isArrayLike(arr)) {
    return [];
  }
  return dropWhileImpl(toArray(arr), predicate);
}
function dropWhileImpl(arr, predicate) {
  switch (typeof predicate) {
    case "function": {
      return dropWhile(arr, (item, index, arr2) => Boolean(predicate(item, index, arr2)));
    }
    case "object": {
      if (Array.isArray(predicate) && predicate.length === 2) {
        const key = predicate[0];
        const value = predicate[1];
        return dropWhile(arr, matchesProperty(key, value));
      } else {
        return dropWhile(arr, matches(predicate));
      }
    }
    case "number":
    case "symbol":
    case "string": {
      return dropWhile(arr, property(predicate));
    }
  }
}

// node_modules/es-toolkit/dist/compat/_internal/isIterateeCall.mjs
function isIterateeCall(value, index, object) {
  if (!isObject(object)) {
    return false;
  }
  if (typeof index === "number" && isArrayLike(object) && isIndex(index) && index < object.length || typeof index === "string" && index in object) {
    return eq(object[index], value);
  }
  return false;
}

// node_modules/es-toolkit/dist/compat/array/every.mjs
function every(source, doesMatch, guard) {
  if (!source) {
    return true;
  }
  if (guard && isIterateeCall(source, doesMatch, guard)) {
    doesMatch = void 0;
  }
  if (!doesMatch) {
    doesMatch = identity;
  }
  let predicate;
  switch (typeof doesMatch) {
    case "function": {
      predicate = doesMatch;
      break;
    }
    case "object": {
      if (Array.isArray(doesMatch) && doesMatch.length === 2) {
        const key = doesMatch[0];
        const value = doesMatch[1];
        predicate = matchesProperty(key, value);
      } else {
        predicate = matches(doesMatch);
      }
      break;
    }
    case "symbol":
    case "number":
    case "string": {
      predicate = property(doesMatch);
    }
  }
  if (!isArrayLike(source)) {
    const keys2 = Object.keys(source);
    for (let i = 0; i < keys2.length; i++) {
      const key = keys2[i];
      const value = source[key];
      if (!predicate(value, key, source)) {
        return false;
      }
    }
    return true;
  }
  for (let i = 0; i < source.length; i++) {
    if (!predicate(source[i], i, source)) {
      return false;
    }
  }
  return true;
}

// node_modules/es-toolkit/dist/compat/predicate/isString.mjs
function isString2(value) {
  return typeof value === "string" || value instanceof String;
}

// node_modules/es-toolkit/dist/compat/array/fill.mjs
function fill2(array, value, start = 0, end = array ? array.length : 0) {
  if (!isArrayLike(array)) {
    return [];
  }
  if (isString2(array)) {
    return array;
  }
  start = Math.floor(start);
  end = Math.floor(end);
  if (!start) {
    start = 0;
  }
  if (!end) {
    end = 0;
  }
  return fill(array, value, start, end);
}

// node_modules/es-toolkit/dist/compat/array/filter.mjs
function filter(source, predicate) {
  if (!source) {
    return [];
  }
  predicate = iteratee(predicate);
  if (!Array.isArray(source)) {
    const result3 = [];
    const keys2 = Object.keys(source);
    const length2 = isArrayLike(source) ? source.length : keys2.length;
    for (let i = 0; i < length2; i++) {
      const key = keys2[i];
      const value = source[key];
      if (predicate(value, key, source)) {
        result3.push(value);
      }
    }
    return result3;
  }
  const result2 = [];
  const length = source.length;
  for (let i = 0; i < length; i++) {
    const value = source[i];
    if (predicate(value, i, source)) {
      result2.push(value);
    }
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/array/find.mjs
function find(source, _doesMatch, fromIndex = 0) {
  if (!source) {
    return void 0;
  }
  if (fromIndex < 0) {
    fromIndex = Math.max(source.length + fromIndex, 0);
  }
  const doesMatch = iteratee(_doesMatch);
  if (typeof doesMatch === "function" && !Array.isArray(source)) {
    const keys2 = Object.keys(source);
    for (let i = fromIndex; i < keys2.length; i++) {
      const key = keys2[i];
      const value = source[key];
      if (doesMatch(value, key, source)) {
        return value;
      }
    }
    return void 0;
  }
  const values2 = Array.isArray(source) ? source.slice(fromIndex) : Object.values(source).slice(fromIndex);
  return values2.find(doesMatch);
}

// node_modules/es-toolkit/dist/compat/array/findIndex.mjs
function findIndex(arr, doesMatch, fromIndex = 0) {
  if (!arr) {
    return -1;
  }
  if (fromIndex < 0) {
    fromIndex = Math.max(arr.length + fromIndex, 0);
  }
  const subArray = Array.from(arr).slice(fromIndex);
  let index = -1;
  switch (typeof doesMatch) {
    case "function": {
      index = subArray.findIndex(doesMatch);
      break;
    }
    case "object": {
      if (Array.isArray(doesMatch) && doesMatch.length === 2) {
        const key = doesMatch[0];
        const value = doesMatch[1];
        index = subArray.findIndex(matchesProperty(key, value));
      } else {
        index = subArray.findIndex(matches(doesMatch));
      }
      break;
    }
    case "number":
    case "symbol":
    case "string": {
      index = subArray.findIndex(property(doesMatch));
    }
  }
  return index === -1 ? -1 : index + fromIndex;
}

// node_modules/es-toolkit/dist/compat/array/findLast.mjs
function findLast(source, _doesMatch, fromIndex) {
  if (!source) {
    return void 0;
  }
  const length = Array.isArray(source) ? source.length : Object.keys(source).length;
  fromIndex = toInteger(fromIndex ?? length - 1);
  if (fromIndex < 0) {
    fromIndex = Math.max(length + fromIndex, 0);
  } else {
    fromIndex = Math.min(fromIndex, length - 1);
  }
  const doesMatch = iteratee(_doesMatch);
  if (typeof doesMatch === "function" && !Array.isArray(source)) {
    const keys2 = Object.keys(source);
    for (let i = fromIndex; i >= 0; i--) {
      const key = keys2[i];
      const value = source[key];
      if (doesMatch(value, key, source)) {
        return value;
      }
    }
    return void 0;
  }
  const values2 = Array.isArray(source) ? source.slice(0, fromIndex + 1) : Object.values(source).slice(0, fromIndex + 1);
  return values2.findLast(doesMatch);
}

// node_modules/es-toolkit/dist/compat/array/findLastIndex.mjs
function findLastIndex(arr, doesMatch, fromIndex = arr ? arr.length - 1 : 0) {
  if (!arr) {
    return -1;
  }
  if (fromIndex < 0) {
    fromIndex = Math.max(arr.length + fromIndex, 0);
  } else {
    fromIndex = Math.min(fromIndex, arr.length - 1);
  }
  const subArray = toArray(arr).slice(0, fromIndex + 1);
  switch (typeof doesMatch) {
    case "function": {
      return subArray.findLastIndex(doesMatch);
    }
    case "object": {
      if (Array.isArray(doesMatch) && doesMatch.length === 2) {
        const key = doesMatch[0];
        const value = doesMatch[1];
        return subArray.findLastIndex(matchesProperty(key, value));
      } else {
        return subArray.findLastIndex(matches(doesMatch));
      }
    }
    case "number":
    case "symbol":
    case "string": {
      return subArray.findLastIndex(property(doesMatch));
    }
  }
}

// node_modules/es-toolkit/dist/compat/array/flatten.mjs
function flatten2(value, depth = 1) {
  const result2 = [];
  const flooredDepth = Math.floor(depth);
  if (!isArrayLike(value)) {
    return result2;
  }
  const recursive = (arr, currentDepth) => {
    for (let i = 0; i < arr.length; i++) {
      const item = arr[i];
      if (currentDepth < flooredDepth && (Array.isArray(item) || Boolean(item == null ? void 0 : item[Symbol.isConcatSpreadable]) || item !== null && typeof item === "object" && Object.prototype.toString.call(item) === "[object Arguments]")) {
        if (Array.isArray(item)) {
          recursive(item, currentDepth + 1);
        } else {
          recursive(Array.from(item), currentDepth + 1);
        }
      } else {
        result2.push(item);
      }
    }
  };
  recursive(Array.from(value), 0);
  return result2;
}

// node_modules/es-toolkit/dist/compat/array/map.mjs
function map(collection, _iteratee) {
  if (!collection) {
    return [];
  }
  const keys2 = isArrayLike(collection) || Array.isArray(collection) ? range(0, collection.length) : Object.keys(collection);
  const iteratee$1 = iteratee(_iteratee ?? identity);
  const result2 = new Array(keys2.length);
  for (let i = 0; i < keys2.length; i++) {
    const key = keys2[i];
    const value = collection[key];
    result2[i] = iteratee$1(value, key, collection);
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/array/flatMap.mjs
function flatMap2(collection, iteratee2) {
  if (isNil(collection)) {
    return [];
  }
  const mapped = isNil(iteratee2) ? map(collection) : map(collection, iteratee2);
  return flatten2(mapped, 1);
}

// node_modules/es-toolkit/dist/compat/array/flattenDeep.mjs
function flattenDeep2(value) {
  return flatten2(value, Infinity);
}

// node_modules/es-toolkit/dist/compat/array/flattenDepth.mjs
function flattenDepth(value, depth = 1) {
  return flatten2(value, depth);
}

// node_modules/es-toolkit/dist/compat/array/forEach.mjs
function forEach(collection, callback = identity) {
  if (!collection) {
    return collection;
  }
  const keys2 = isArrayLike(collection) || Array.isArray(collection) ? range(0, collection.length) : Object.keys(collection);
  for (let i = 0; i < keys2.length; i++) {
    const key = keys2[i];
    const value = collection[key];
    const result2 = callback(value, key, collection);
    if (result2 === false) {
      break;
    }
  }
  return collection;
}

// node_modules/es-toolkit/dist/compat/array/forEachRight.mjs
function forEachRight2(collection, callback = identity) {
  if (!collection) {
    return collection;
  }
  const keys2 = isArrayLike(collection) ? range(0, collection.length) : Object.keys(collection);
  for (let i = keys2.length - 1; i >= 0; i--) {
    const key = keys2[i];
    const value = collection[key];
    const result2 = callback(value, key, collection);
    if (result2 === false) {
      break;
    }
  }
  return collection;
}

// node_modules/es-toolkit/dist/compat/array/groupBy.mjs
function groupBy2(source, _getKeyFromItem) {
  if (source == null) {
    return {};
  }
  const items = isArrayLike(source) ? Array.from(source) : Object.values(source);
  const getKeyFromItem = iteratee(_getKeyFromItem ?? identity);
  return groupBy(items, getKeyFromItem);
}

// node_modules/es-toolkit/dist/compat/array/head.mjs
function head2(arr) {
  if (!isArrayLike(arr)) {
    return void 0;
  }
  return head(toArray(arr));
}

// node_modules/es-toolkit/dist/compat/array/includes.mjs
function includes(source, target, fromIndex, guard) {
  if (source == null) {
    return false;
  }
  if (guard || !fromIndex) {
    fromIndex = 0;
  } else {
    fromIndex = toInteger(fromIndex);
  }
  if (isString2(source)) {
    if (fromIndex > source.length || target instanceof RegExp) {
      return false;
    }
    if (fromIndex < 0) {
      fromIndex = Math.max(0, source.length + fromIndex);
    }
    return source.includes(target, fromIndex);
  }
  if (Array.isArray(source)) {
    return source.includes(target, fromIndex);
  }
  const keys2 = Object.keys(source);
  if (fromIndex < 0) {
    fromIndex = Math.max(0, keys2.length + fromIndex);
  }
  for (let i = fromIndex; i < keys2.length; i++) {
    const value = Reflect.get(source, keys2[i]);
    if (eq(value, target)) {
      return true;
    }
  }
  return false;
}

// node_modules/es-toolkit/dist/compat/array/indexOf.mjs
function indexOf(array, searchElement, fromIndex) {
  if (!isArrayLike(array)) {
    return -1;
  }
  if (Number.isNaN(searchElement)) {
    fromIndex = fromIndex ?? 0;
    if (fromIndex < 0) {
      fromIndex = Math.max(0, array.length + fromIndex);
    }
    for (let i = fromIndex; i < array.length; i++) {
      if (Number.isNaN(array[i])) {
        return i;
      }
    }
    return -1;
  }
  return Array.from(array).indexOf(searchElement, fromIndex);
}

// node_modules/es-toolkit/dist/compat/array/initial.mjs
function initial2(arr) {
  if (!isArrayLike(arr)) {
    return [];
  }
  return initial(Array.from(arr));
}

// node_modules/es-toolkit/dist/compat/array/intersection.mjs
function intersection2(...arrays) {
  if (arrays.length === 0) {
    return [];
  }
  if (!isArrayLikeObject(arrays[0])) {
    return [];
  }
  let result2 = uniq(Array.from(arrays[0]));
  for (let i = 1; i < arrays.length; i++) {
    const array = arrays[i];
    if (!isArrayLikeObject(array)) {
      return [];
    }
    result2 = intersection(result2, Array.from(array));
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/array/intersectionBy.mjs
function intersectionBy2(array, ...values2) {
  if (!isArrayLikeObject(array)) {
    return [];
  }
  const lastValue = last(values2);
  if (lastValue === void 0) {
    return Array.from(array);
  }
  let result2 = uniq(Array.from(array));
  const count = isArrayLikeObject(lastValue) ? values2.length : values2.length - 1;
  for (let i = 0; i < count; ++i) {
    const value = values2[i];
    if (!isArrayLikeObject(value)) {
      return [];
    }
    if (isArrayLikeObject(lastValue)) {
      result2 = intersectionBy(result2, Array.from(value), identity);
    } else if (typeof lastValue === "function") {
      result2 = intersectionBy(result2, Array.from(value), (value2) => lastValue(value2));
    } else if (typeof lastValue === "string") {
      result2 = intersectionBy(result2, Array.from(value), property(lastValue));
    }
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/array/uniq.mjs
function uniq2(arr) {
  if (!isArrayLike(arr)) {
    return [];
  }
  return uniq(Array.from(arr));
}

// node_modules/es-toolkit/dist/compat/array/intersectionWith.mjs
function intersectionWith2(firstArr, ...otherArrs) {
  if (firstArr == null) {
    return [];
  }
  const _comparator = last2(otherArrs);
  let comparator = eq;
  let uniq$1 = uniq2;
  if (typeof _comparator === "function") {
    comparator = _comparator;
    uniq$1 = uniqPreserve0;
    otherArrs.pop();
  }
  let result2 = uniq$1(Array.from(firstArr));
  for (let i = 0; i < otherArrs.length; ++i) {
    const otherArr = otherArrs[i];
    if (otherArr == null) {
      return [];
    }
    result2 = intersectionWith(result2, Array.from(otherArr), comparator);
  }
  return result2;
}
function uniqPreserve0(arr) {
  const result2 = [];
  const added = /* @__PURE__ */ new Set();
  for (let i = 0; i < arr.length; i++) {
    const item = arr[i];
    if (added.has(item)) {
      continue;
    }
    result2.push(item);
    added.add(item);
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/array/invokeMap.mjs
function invokeMap(collection, path, ...args) {
  if (isNil(collection)) {
    return [];
  }
  const values2 = isArrayLike(collection) ? Array.from(collection) : Object.values(collection);
  const result2 = [];
  for (let i = 0; i < values2.length; i++) {
    const value = values2[i];
    if (isFunction(path)) {
      result2.push(path.apply(value, args));
      continue;
    }
    const method2 = get(value, path);
    let thisContext = value;
    if (Array.isArray(path)) {
      const pathExceptLast = path.slice(0, -1);
      if (pathExceptLast.length > 0) {
        thisContext = get(value, pathExceptLast);
      }
    } else if (typeof path === "string" && path.includes(".")) {
      const parts = path.split(".");
      const pathExceptLast = parts.slice(0, -1).join(".");
      thisContext = get(value, pathExceptLast);
    }
    result2.push(method2 == null ? void 0 : method2.apply(thisContext, args));
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/array/join.mjs
function join(array, separator = ",") {
  if (!isArrayLike(array)) {
    return "";
  }
  return Array.from(array).join(separator);
}

// node_modules/es-toolkit/dist/compat/array/reduce.mjs
function reduce(collection, iteratee2 = identity, accumulator) {
  if (!collection) {
    return accumulator;
  }
  let keys2;
  let startIndex = 0;
  if (isArrayLike(collection)) {
    keys2 = range(0, collection.length);
    if (accumulator == null && collection.length > 0) {
      accumulator = collection[0];
      startIndex += 1;
    }
  } else {
    keys2 = Object.keys(collection);
    if (accumulator == null) {
      accumulator = collection[keys2[0]];
      startIndex += 1;
    }
  }
  for (let i = startIndex; i < keys2.length; i++) {
    const key = keys2[i];
    const value = collection[key];
    accumulator = iteratee2(accumulator, value, key, collection);
  }
  return accumulator;
}

// node_modules/es-toolkit/dist/compat/array/keyBy.mjs
function keyBy2(collection, iteratee$1) {
  if (!isArrayLike(collection) && !isObjectLike(collection)) {
    return {};
  }
  const keyFn = iteratee(iteratee$1 ?? identity);
  return reduce(collection, (result2, value) => {
    const key = keyFn(value);
    result2[key] = value;
    return result2;
  }, {});
}

// node_modules/es-toolkit/dist/compat/array/lastIndexOf.mjs
function lastIndexOf(array, searchElement, fromIndex) {
  if (!isArrayLike(array) || array.length === 0) {
    return -1;
  }
  const length = array.length;
  let index = fromIndex ?? length - 1;
  if (fromIndex != null) {
    index = index < 0 ? Math.max(length + index, 0) : Math.min(index, length - 1);
  }
  if (Number.isNaN(searchElement)) {
    for (let i = index; i >= 0; i--) {
      if (Number.isNaN(array[i])) {
        return i;
      }
    }
  }
  return Array.from(array).lastIndexOf(searchElement, index);
}

// node_modules/es-toolkit/dist/compat/array/nth.mjs
function nth(array, n2 = 0) {
  if (!isArrayLikeObject(array) || array.length === 0) {
    return void 0;
  }
  n2 = toInteger(n2);
  if (n2 < 0) {
    n2 += array.length;
  }
  return array[n2];
}

// node_modules/es-toolkit/dist/compat/_internal/compareValues.mjs
function getPriority(a2) {
  if (typeof a2 === "symbol") {
    return 1;
  }
  if (a2 === null) {
    return 2;
  }
  if (a2 === void 0) {
    return 3;
  }
  if (a2 !== a2) {
    return 4;
  }
  return 0;
}
var compareValues2 = (a2, b2, order) => {
  if (a2 !== b2) {
    const aPriority = getPriority(a2);
    const bPriority = getPriority(b2);
    if (aPriority === bPriority && aPriority === 0) {
      if (a2 < b2) {
        return order === "desc" ? 1 : -1;
      }
      if (a2 > b2) {
        return order === "desc" ? -1 : 1;
      }
    }
    return order === "desc" ? bPriority - aPriority : aPriority - bPriority;
  }
  return 0;
};

// node_modules/es-toolkit/dist/compat/_internal/isKey.mjs
var regexIsDeepProp = /\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/;
var regexIsPlainProp = /^\w*$/;
function isKey(value, object) {
  if (Array.isArray(value)) {
    return false;
  }
  if (typeof value === "number" || typeof value === "boolean" || value == null || isSymbol(value)) {
    return true;
  }
  return typeof value === "string" && (regexIsPlainProp.test(value) || !regexIsDeepProp.test(value)) || object != null && Object.hasOwn(object, value);
}

// node_modules/es-toolkit/dist/compat/array/orderBy.mjs
function orderBy2(collection, criteria, orders, guard) {
  if (collection == null) {
    return [];
  }
  orders = guard ? void 0 : orders;
  if (!Array.isArray(collection)) {
    collection = Object.values(collection);
  }
  if (!Array.isArray(criteria)) {
    criteria = criteria == null ? [null] : [criteria];
  }
  if (criteria.length === 0) {
    criteria = [null];
  }
  if (!Array.isArray(orders)) {
    orders = orders == null ? [] : [orders];
  }
  orders = orders.map((order) => String(order));
  const getValueByNestedPath = (object, path) => {
    let target = object;
    for (let i = 0; i < path.length && target != null; ++i) {
      target = target[path[i]];
    }
    return target;
  };
  const getValueByCriterion = (criterion, object) => {
    if (object == null || criterion == null) {
      return object;
    }
    if (typeof criterion === "object" && "key" in criterion) {
      if (Object.hasOwn(object, criterion.key)) {
        return object[criterion.key];
      }
      return getValueByNestedPath(object, criterion.path);
    }
    if (typeof criterion === "function") {
      return criterion(object);
    }
    if (Array.isArray(criterion)) {
      return getValueByNestedPath(object, criterion);
    }
    if (typeof object === "object") {
      return object[criterion];
    }
    return object;
  };
  const preparedCriteria = criteria.map((criterion) => {
    if (Array.isArray(criterion) && criterion.length === 1) {
      criterion = criterion[0];
    }
    if (criterion == null || typeof criterion === "function" || Array.isArray(criterion) || isKey(criterion)) {
      return criterion;
    }
    return { key: criterion, path: toPath(criterion) };
  });
  const preparedCollection = collection.map((item) => ({
    original: item,
    criteria: preparedCriteria.map((criterion) => getValueByCriterion(criterion, item))
  }));
  return preparedCollection.slice().sort((a2, b2) => {
    for (let i = 0; i < preparedCriteria.length; i++) {
      const comparedResult = compareValues2(a2.criteria[i], b2.criteria[i], orders[i]);
      if (comparedResult !== 0) {
        return comparedResult;
      }
    }
    return 0;
  }).map((item) => item.original);
}

// node_modules/es-toolkit/dist/compat/array/partition.mjs
function partition2(source, predicate) {
  if (!source) {
    return [[], []];
  }
  const collection = isArrayLike(source) ? source : Object.values(source);
  predicate = iteratee(predicate);
  const matched = [];
  const unmatched = [];
  for (let i = 0; i < collection.length; i++) {
    const value = collection[i];
    if (predicate(value)) {
      matched.push(value);
    } else {
      unmatched.push(value);
    }
  }
  return [matched, unmatched];
}

// node_modules/es-toolkit/dist/compat/array/pull.mjs
function pull2(arr, ...valuesToRemove) {
  return pull(arr, valuesToRemove);
}

// node_modules/es-toolkit/dist/compat/array/pullAll.mjs
function pullAll(arr, valuesToRemove = []) {
  return pull(arr, Array.from(valuesToRemove));
}

// node_modules/es-toolkit/dist/compat/array/pullAllBy.mjs
function pullAllBy(arr, valuesToRemove, _getValue) {
  const getValue = iteratee(_getValue);
  const valuesSet = new Set(Array.from(valuesToRemove).map((x3) => getValue(x3)));
  let resultIndex = 0;
  for (let i = 0; i < arr.length; i++) {
    const value = getValue(arr[i]);
    if (valuesSet.has(value)) {
      continue;
    }
    if (!Object.hasOwn(arr, i)) {
      delete arr[resultIndex++];
      continue;
    }
    arr[resultIndex++] = arr[i];
  }
  arr.length = resultIndex;
  return arr;
}

// node_modules/es-toolkit/dist/compat/_internal/copyArray.mjs
function copyArray(source, array) {
  const length = source.length;
  if (array == null) {
    array = Array(length);
  }
  for (let i = 0; i < length; i++) {
    array[i] = source[i];
  }
  return array;
}

// node_modules/es-toolkit/dist/compat/array/pullAllWith.mjs
function pullAllWith(array, values2, comparator) {
  if ((array == null ? void 0 : array.length) == null || (values2 == null ? void 0 : values2.length) == null) {
    return array;
  }
  if (array === values2) {
    values2 = copyArray(values2);
  }
  let resultLength = 0;
  if (comparator == null) {
    comparator = (a2, b2) => eq(a2, b2);
  }
  const valuesArray = Array.isArray(values2) ? values2 : Array.from(values2);
  const hasUndefined = valuesArray.includes(void 0);
  for (let i = 0; i < array.length; i++) {
    if (i in array) {
      const shouldRemove = valuesArray.some((value) => comparator(array[i], value));
      if (!shouldRemove) {
        array[resultLength++] = array[i];
      }
      continue;
    }
    if (!hasUndefined) {
      delete array[resultLength++];
    }
  }
  array.length = resultLength;
  return array;
}

// node_modules/es-toolkit/dist/compat/object/at.mjs
function at3(object, ...paths) {
  if (paths.length === 0) {
    return [];
  }
  const allPaths = [];
  for (let i = 0; i < paths.length; i++) {
    const path = paths[i];
    if (!isArrayLike(path) || isString2(path)) {
      allPaths.push(path);
      continue;
    }
    for (let j2 = 0; j2 < path.length; j2++) {
      allPaths.push(path[j2]);
    }
  }
  const result2 = [];
  for (let i = 0; i < allPaths.length; i++) {
    result2.push(get(object, allPaths[i]));
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/object/unset.mjs
function unset(obj, path) {
  if (obj == null) {
    return true;
  }
  switch (typeof path) {
    case "symbol":
    case "number":
    case "object": {
      if (Array.isArray(path)) {
        return unsetWithPath(obj, path);
      }
      if (typeof path === "number") {
        path = toKey(path);
      } else if (typeof path === "object") {
        if (Object.is(path == null ? void 0 : path.valueOf(), -0)) {
          path = "-0";
        } else {
          path = String(path);
        }
      }
      if ((obj == null ? void 0 : obj[path]) === void 0) {
        return true;
      }
      try {
        delete obj[path];
        return true;
      } catch {
        return false;
      }
    }
    case "string": {
      if ((obj == null ? void 0 : obj[path]) === void 0 && isDeepKey(path)) {
        return unsetWithPath(obj, toPath(path));
      }
      try {
        delete obj[path];
        return true;
      } catch {
        return false;
      }
    }
  }
}
function unsetWithPath(obj, path) {
  const parent = get(obj, path.slice(0, -1), obj);
  const lastKey = path[path.length - 1];
  if ((parent == null ? void 0 : parent[lastKey]) === void 0) {
    return true;
  }
  try {
    delete parent[lastKey];
    return true;
  } catch {
    return false;
  }
}

// node_modules/es-toolkit/dist/compat/array/pullAt.mjs
function pullAt2(array, ..._indices) {
  const indices = flatten2(_indices, 1);
  if (!array) {
    return Array(indices.length);
  }
  const result2 = at3(array, indices);
  const indicesToPull = indices.map((index) => isIndex(index, array.length) ? Number(index) : index).sort((a2, b2) => b2 - a2);
  for (const index of new Set(indicesToPull)) {
    if (isIndex(index, array.length)) {
      Array.prototype.splice.call(array, index, 1);
      continue;
    }
    if (isKey(index, array)) {
      delete array[toKey(index)];
      continue;
    }
    const path = isArray(index) ? index : toPath(index);
    unset(array, path);
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/array/reduceRight.mjs
function reduceRight(collection, iteratee2 = identity, accumulator) {
  if (!collection) {
    return accumulator;
  }
  let keys2;
  let startIndex;
  if (isArrayLike(collection)) {
    keys2 = range(0, collection.length).reverse();
    if (accumulator == null && collection.length > 0) {
      accumulator = collection[collection.length - 1];
      startIndex = 1;
    } else {
      startIndex = 0;
    }
  } else {
    keys2 = Object.keys(collection).reverse();
    if (accumulator == null) {
      accumulator = collection[keys2[0]];
      startIndex = 1;
    } else {
      startIndex = 0;
    }
  }
  for (let i = startIndex; i < keys2.length; i++) {
    const key = keys2[i];
    const value = collection[key];
    accumulator = iteratee2(accumulator, value, key, collection);
  }
  return accumulator;
}

// node_modules/es-toolkit/dist/compat/function/negate.mjs
function negate2(func) {
  if (typeof func !== "function") {
    throw new TypeError("Expected a function");
  }
  return function(...args) {
    return !func.apply(this, args);
  };
}

// node_modules/es-toolkit/dist/compat/array/reject.mjs
function reject(source, predicate) {
  return filter(source, negate2(iteratee(predicate)));
}

// node_modules/es-toolkit/dist/compat/array/remove.mjs
function remove2(arr, shouldRemoveElement) {
  return remove(arr, iteratee(shouldRemoveElement));
}

// node_modules/es-toolkit/dist/compat/array/reverse.mjs
function reverse(array) {
  if (array == null) {
    return array;
  }
  return array.reverse();
}

// node_modules/es-toolkit/dist/compat/array/sample.mjs
function sample2(collection) {
  if (collection == null) {
    return void 0;
  }
  if (isArrayLike(collection)) {
    return sample(toArray(collection));
  }
  return sample(Object.values(collection));
}

// node_modules/es-toolkit/dist/compat/math/clamp.mjs
function clamp2(value, bound1, bound2) {
  if (Number.isNaN(bound1)) {
    bound1 = 0;
  }
  if (Number.isNaN(bound2)) {
    bound2 = 0;
  }
  return clamp(value, bound1, bound2);
}

// node_modules/es-toolkit/dist/compat/predicate/isMap.mjs
function isMap2(value) {
  return isMap(value);
}

// node_modules/es-toolkit/dist/compat/util/toArray.mjs
function toArray2(value) {
  if (value == null) {
    return [];
  }
  if (isArrayLike(value) || isMap2(value)) {
    return Array.from(value);
  }
  if (typeof value === "object") {
    return Object.values(value);
  }
  return [];
}

// node_modules/es-toolkit/dist/compat/array/sampleSize.mjs
function sampleSize2(collection, size2, guard) {
  const arrayCollection = toArray2(collection);
  if (guard ? isIterateeCall(collection, size2, guard) : size2 === void 0) {
    size2 = 1;
  } else {
    size2 = clamp2(toInteger(size2), 0, arrayCollection.length);
  }
  return sampleSize(arrayCollection, size2);
}

// node_modules/es-toolkit/dist/compat/object/values.mjs
function values(object) {
  return Object.values(object);
}

// node_modules/es-toolkit/dist/compat/predicate/isNil.mjs
function isNil2(x3) {
  return x3 == null;
}

// node_modules/es-toolkit/dist/compat/array/shuffle.mjs
function shuffle2(collection) {
  if (isNil2(collection)) {
    return [];
  }
  if (isArray(collection)) {
    return shuffle(collection);
  }
  if (isArrayLike(collection)) {
    return shuffle(Array.from(collection));
  }
  if (isObjectLike(collection)) {
    return shuffle(values(collection));
  }
  return [];
}

// node_modules/es-toolkit/dist/compat/array/size.mjs
function size(target) {
  if (isNil(target)) {
    return 0;
  }
  if (target instanceof Map || target instanceof Set) {
    return target.size;
  }
  return Object.keys(target).length;
}

// node_modules/es-toolkit/dist/compat/array/slice.mjs
function slice(array, start, end) {
  if (!isArrayLike(array)) {
    return [];
  }
  const length = array.length;
  if (end === void 0) {
    end = length;
  } else if (typeof end !== "number" && isIterateeCall(array, start, end)) {
    start = 0;
    end = length;
  }
  start = toInteger(start);
  end = toInteger(end);
  if (start < 0) {
    start = Math.max(length + start, 0);
  } else {
    start = Math.min(start, length);
  }
  if (end < 0) {
    end = Math.max(length + end, 0);
  } else {
    end = Math.min(end, length);
  }
  const resultLength = Math.max(end - start, 0);
  const result2 = new Array(resultLength);
  for (let i = 0; i < resultLength; ++i) {
    result2[i] = array[start + i];
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/array/some.mjs
function some(source, predicate, guard) {
  if (!source) {
    return false;
  }
  if (guard != null) {
    predicate = void 0;
  }
  if (!predicate) {
    predicate = identity;
  }
  const values2 = Array.isArray(source) ? source : Object.values(source);
  switch (typeof predicate) {
    case "function": {
      if (!Array.isArray(source)) {
        const keys2 = Object.keys(source);
        for (let i = 0; i < keys2.length; i++) {
          const key = keys2[i];
          const value = source[key];
          if (predicate(value, key, source)) {
            return true;
          }
        }
        return false;
      }
      for (let i = 0; i < source.length; i++) {
        if (predicate(source[i], i, source)) {
          return true;
        }
      }
      return false;
    }
    case "object": {
      if (Array.isArray(predicate) && predicate.length === 2) {
        const key = predicate[0];
        const value = predicate[1];
        const matchFunc = matchesProperty(key, value);
        if (Array.isArray(source)) {
          for (let i = 0; i < source.length; i++) {
            if (matchFunc(source[i])) {
              return true;
            }
          }
          return false;
        }
        return values2.some(matchFunc);
      } else {
        const matchFunc = matches(predicate);
        if (Array.isArray(source)) {
          for (let i = 0; i < source.length; i++) {
            if (matchFunc(source[i])) {
              return true;
            }
          }
          return false;
        }
        return values2.some(matchFunc);
      }
    }
    case "number":
    case "symbol":
    case "string": {
      const propFunc = property(predicate);
      if (Array.isArray(source)) {
        for (let i = 0; i < source.length; i++) {
          if (propFunc(source[i])) {
            return true;
          }
        }
        return false;
      }
      return values2.some(propFunc);
    }
  }
}

// node_modules/es-toolkit/dist/compat/array/sortBy.mjs
function sortBy2(collection, ...criteria) {
  const length = criteria.length;
  if (length > 1 && isIterateeCall(collection, criteria[0], criteria[1])) {
    criteria = [];
  } else if (length > 2 && isIterateeCall(criteria[0], criteria[1], criteria[2])) {
    criteria = [criteria[0]];
  }
  return orderBy2(collection, flatten(criteria), ["asc"]);
}

// node_modules/es-toolkit/dist/compat/predicate/isNaN.mjs
function isNaN2(value) {
  return Number.isNaN(value);
}

// node_modules/es-toolkit/dist/compat/array/sortedIndexBy.mjs
var MAX_ARRAY_LENGTH = 4294967295;
var MAX_ARRAY_INDEX = MAX_ARRAY_LENGTH - 1;
function sortedIndexBy(array, value, iteratee$1, retHighest) {
  let low = 0;
  let high = array == null ? 0 : array.length;
  if (high === 0 || isNil2(array)) {
    return 0;
  }
  const iterateeFunction = iteratee(iteratee$1);
  const transformedValue = iterateeFunction(value);
  const valIsNaN = isNaN2(transformedValue);
  const valIsNull = isNull(transformedValue);
  const valIsSymbol = isSymbol(transformedValue);
  const valIsUndefined = isUndefined(transformedValue);
  while (low < high) {
    let setLow;
    const mid = Math.floor((low + high) / 2);
    const computed = iterateeFunction(array[mid]);
    const othIsDefined = !isUndefined(computed);
    const othIsNull = isNull(computed);
    const othIsReflexive = !isNaN2(computed);
    const othIsSymbol = isSymbol(computed);
    if (valIsNaN) {
      setLow = retHighest || othIsReflexive;
    } else if (valIsUndefined) {
      setLow = othIsReflexive && (retHighest || othIsDefined);
    } else if (valIsNull) {
      setLow = othIsReflexive && othIsDefined && (retHighest || !othIsNull);
    } else if (valIsSymbol) {
      setLow = othIsReflexive && othIsDefined && !othIsNull && (retHighest || !othIsSymbol);
    } else if (othIsNull || othIsSymbol) {
      setLow = false;
    } else {
      setLow = retHighest ? computed <= transformedValue : computed < transformedValue;
    }
    if (setLow) {
      low = mid + 1;
    } else {
      high = mid;
    }
  }
  return Math.min(high, MAX_ARRAY_INDEX);
}

// node_modules/es-toolkit/dist/compat/predicate/isNumber.mjs
function isNumber(value) {
  return typeof value === "number" || value instanceof Number;
}

// node_modules/es-toolkit/dist/compat/array/sortedIndex.mjs
var MAX_ARRAY_LENGTH2 = 4294967295;
var HALF_MAX_ARRAY_LENGTH = MAX_ARRAY_LENGTH2 >>> 1;
function sortedIndex(array, value) {
  if (isNil(array)) {
    return 0;
  }
  let low = 0, high = isNil(array) ? low : array.length;
  if (isNumber(value) && value === value && high <= HALF_MAX_ARRAY_LENGTH) {
    while (low < high) {
      const mid = low + high >>> 1;
      const compute = array[mid];
      if (!isNull(compute) && !isSymbol2(compute) && compute < value) {
        low = mid + 1;
      } else {
        high = mid;
      }
    }
    return high;
  }
  return sortedIndexBy(array, value, (value2) => value2);
}

// node_modules/es-toolkit/dist/compat/array/sortedIndexOf.mjs
function sortedIndexOf(array, value) {
  if (!(array == null ? void 0 : array.length)) {
    return -1;
  }
  const index = sortedIndex(array, value);
  if (index < array.length && eq(array[index], value)) {
    return index;
  }
  return -1;
}

// node_modules/es-toolkit/dist/compat/array/sortedLastIndexBy.mjs
function sortedLastIndexBy(array, value, iteratee2) {
  return sortedIndexBy(array, value, iteratee2, true);
}

// node_modules/es-toolkit/dist/compat/array/sortedLastIndex.mjs
var MAX_ARRAY_LENGTH3 = 4294967295;
var HALF_MAX_ARRAY_LENGTH2 = MAX_ARRAY_LENGTH3 >>> 1;
function sortedLastIndex(array, value) {
  if (isNil(array)) {
    return 0;
  }
  let high = array.length;
  if (!isNumber(value) || Number.isNaN(value) || high > HALF_MAX_ARRAY_LENGTH2) {
    return sortedLastIndexBy(array, value, (value2) => value2);
  }
  let low = 0;
  while (low < high) {
    const mid = low + high >>> 1;
    const compute = array[mid];
    if (!isNull(compute) && !isSymbol2(compute) && compute <= value) {
      low = mid + 1;
    } else {
      high = mid;
    }
  }
  return high;
}

// node_modules/es-toolkit/dist/compat/array/sortedLastIndexOf.mjs
function sortedLastIndexOf(array, value) {
  if (!(array == null ? void 0 : array.length)) {
    return -1;
  }
  const index = sortedLastIndex(array, value) - 1;
  if (index >= 0 && eq(array[index], value)) {
    return index;
  }
  return -1;
}

// node_modules/es-toolkit/dist/compat/array/tail.mjs
function tail2(arr) {
  if (!isArrayLike(arr)) {
    return [];
  }
  return tail(toArray(arr));
}

// node_modules/es-toolkit/dist/compat/array/take.mjs
function take2(arr, count = 1, guard) {
  count = guard ? 1 : toInteger(count);
  if (count < 1 || !isArrayLike(arr)) {
    return [];
  }
  return take(toArray(arr), count);
}

// node_modules/es-toolkit/dist/compat/array/takeRight.mjs
function takeRight2(arr, count = 1, guard) {
  count = guard ? 1 : toInteger(count);
  if (count <= 0 || !isArrayLike(arr)) {
    return [];
  }
  return takeRight(toArray(arr), count);
}

// node_modules/es-toolkit/dist/compat/array/takeRightWhile.mjs
function takeRightWhile2(_array, predicate) {
  if (!isArrayLikeObject(_array)) {
    return [];
  }
  const array = toArray(_array);
  const index = array.findLastIndex(negate(iteratee(predicate)));
  return array.slice(index + 1);
}

// node_modules/es-toolkit/dist/compat/array/takeWhile.mjs
function takeWhile2(array, predicate) {
  if (!isArrayLikeObject(array)) {
    return [];
  }
  const _array = toArray(array);
  const index = _array.findIndex(negate2(iteratee(predicate)));
  return index === -1 ? _array : _array.slice(0, index);
}

// node_modules/es-toolkit/dist/compat/array/union.mjs
function union2(...arrays) {
  const validArrays = arrays.filter(isArrayLikeObject);
  const flattened = flatten2(validArrays, 1);
  return uniq(flattened);
}

// node_modules/es-toolkit/dist/compat/array/unionBy.mjs
function unionBy2(...values2) {
  const lastValue = last(values2);
  const flattened = flattenArrayLike(values2);
  if (isArrayLikeObject(lastValue) || lastValue == null) {
    return uniq(flattened);
  }
  return uniqBy(flattened, iteratee(lastValue));
}

// node_modules/es-toolkit/dist/compat/array/unionWith.mjs
function unionWith2(...values2) {
  const lastValue = last(values2);
  const flattened = flattenArrayLike(values2);
  if (isArrayLikeObject(lastValue) || lastValue == null) {
    return uniq(flattened);
  }
  return uniqWith(flattened, lastValue);
}

// node_modules/es-toolkit/dist/compat/array/uniqBy.mjs
function uniqBy2(array, iteratee$1) {
  if (!isArrayLikeObject(array)) {
    return [];
  }
  return uniqBy(Array.from(array), iteratee(iteratee$1));
}

// node_modules/es-toolkit/dist/compat/array/uniqWith.mjs
function uniqWith2(arr, comparator) {
  if (!isArrayLike(arr)) {
    return [];
  }
  return typeof comparator === "function" ? uniqWith(Array.from(arr), comparator) : uniq2(Array.from(arr));
}

// node_modules/es-toolkit/dist/compat/array/unzip.mjs
function unzip2(array) {
  if (!isArrayLikeObject(array) || !array.length) {
    return [];
  }
  array = isArray(array) ? array : Array.from(array);
  array = array.filter((item) => isArrayLikeObject(item));
  return unzip(array);
}

// node_modules/es-toolkit/dist/compat/array/unzipWith.mjs
function unzipWith2(array, iteratee2) {
  if (!isArrayLikeObject(array) || !array.length) {
    return [];
  }
  const unziped = isArray(array) ? unzip(array) : unzip(Array.from(array, (value) => Array.from(value)));
  if (!iteratee2) {
    return unziped;
  }
  const result2 = new Array(unziped.length);
  for (let i = 0; i < unziped.length; i++) {
    const value = unziped[i];
    result2[i] = iteratee2(...value);
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/array/without.mjs
function without2(array, ...values2) {
  if (!isArrayLikeObject(array)) {
    return [];
  }
  return without(Array.from(array), ...values2);
}

// node_modules/es-toolkit/dist/compat/array/xor.mjs
function xor2(...arrays) {
  const itemCounts = /* @__PURE__ */ new Map();
  for (let i = 0; i < arrays.length; i++) {
    const array = arrays[i];
    if (!isArrayLikeObject(array)) {
      continue;
    }
    const itemSet = new Set(toArray2(array));
    for (const item of itemSet) {
      if (!itemCounts.has(item)) {
        itemCounts.set(item, 1);
      } else {
        itemCounts.set(item, itemCounts.get(item) + 1);
      }
    }
  }
  const result2 = [];
  for (const [item, count] of itemCounts) {
    if (count === 1) {
      result2.push(item);
    }
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/array/xorBy.mjs
function xorBy2(...values2) {
  const lastValue = last2(values2);
  let mapper = identity;
  if (!isArrayLikeObject(lastValue) && lastValue != null) {
    mapper = iteratee(lastValue);
    values2 = values2.slice(0, -1);
  }
  const arrays = values2.filter(isArrayLikeObject);
  const union3 = unionBy2(...arrays, mapper);
  const intersections = windowed(arrays, 2).map(([arr1, arr2]) => intersectionBy2(arr1, arr2, mapper));
  return differenceBy2(union3, unionBy2(...intersections, mapper), mapper);
}

// node_modules/es-toolkit/dist/compat/array/xorWith.mjs
function xorWith2(...values2) {
  const lastValue = last2(values2);
  let comparator = (a2, b2) => a2 === b2;
  if (typeof lastValue === "function") {
    comparator = lastValue;
    values2 = values2.slice(0, -1);
  }
  const arrays = values2.filter(isArrayLikeObject);
  const union3 = unionWith2(...arrays, comparator);
  const intersections = windowed(arrays, 2).map(([arr1, arr2]) => intersectionWith2(arr1, arr2, comparator));
  return differenceWith2(union3, unionWith2(...intersections, comparator), comparator);
}

// node_modules/es-toolkit/dist/compat/array/zip.mjs
function zip2(...arrays) {
  if (!arrays.length) {
    return [];
  }
  return zip(...arrays.filter((group) => isArrayLikeObject(group)));
}

// node_modules/es-toolkit/dist/compat/_internal/assignValue.mjs
var assignValue = (object, key, value) => {
  const objValue = object[key];
  if (!(Object.hasOwn(object, key) && eq(objValue, value)) || value === void 0 && !(key in object)) {
    object[key] = value;
  }
};

// node_modules/es-toolkit/dist/compat/array/zipObject.mjs
function zipObject2(keys2 = [], values2 = []) {
  const result2 = {};
  for (let i = 0; i < keys2.length; i++) {
    assignValue(result2, keys2[i], values2[i]);
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/object/updateWith.mjs
function updateWith(obj, path, updater, customizer) {
  if (obj == null && !isObject(obj)) {
    return obj;
  }
  const resolvedPath = isKey(path, obj) ? [path] : Array.isArray(path) ? path : typeof path === "string" ? toPath(path) : [path];
  let current = obj;
  for (let i = 0; i < resolvedPath.length && current != null; i++) {
    const key = toKey(resolvedPath[i]);
    let newValue;
    if (i === resolvedPath.length - 1) {
      newValue = updater(current[key]);
    } else {
      const objValue = current[key];
      const customizerResult = customizer(objValue);
      newValue = customizerResult !== void 0 ? customizerResult : isObject(objValue) ? objValue : isIndex(resolvedPath[i + 1]) ? [] : {};
    }
    assignValue(current, key, newValue);
    current = current[key];
  }
  return obj;
}

// node_modules/es-toolkit/dist/compat/object/set.mjs
function set(obj, path, value) {
  return updateWith(obj, path, () => value, () => void 0);
}

// node_modules/es-toolkit/dist/compat/array/zipObjectDeep.mjs
function zipObjectDeep(keys2, values2) {
  const result2 = {};
  if (!isArrayLike(keys2)) {
    return result2;
  }
  if (!isArrayLike(values2)) {
    values2 = [];
  }
  const zipped = zip(Array.from(keys2), Array.from(values2));
  for (let i = 0; i < zipped.length; i++) {
    const [key, value] = zipped[i];
    if (key != null) {
      set(result2, key, value);
    }
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/array/zipWith.mjs
function zipWith2(...combine) {
  let iteratee2 = combine.pop();
  if (!isFunction(iteratee2)) {
    combine.push(iteratee2);
    iteratee2 = void 0;
  }
  if (!(combine == null ? void 0 : combine.length)) {
    return [];
  }
  const result2 = unzip2(combine);
  if (iteratee2 == null) {
    return result2;
  }
  return result2.map((group) => iteratee2(...group));
}

// node_modules/es-toolkit/dist/compat/function/after.mjs
function after2(n2, func) {
  if (typeof func !== "function") {
    throw new TypeError("Expected a function");
  }
  n2 = toInteger(n2);
  return function(...args) {
    if (--n2 < 1) {
      return func.apply(this, args);
    }
  };
}

// node_modules/es-toolkit/dist/compat/function/ary.mjs
function ary2(func, n2 = func.length, guard) {
  if (guard) {
    n2 = func.length;
  }
  if (Number.isNaN(n2) || n2 < 0) {
    n2 = 0;
  }
  return ary(func, n2);
}

// node_modules/es-toolkit/dist/compat/function/attempt.mjs
function attempt2(func, ...args) {
  try {
    return func(...args);
  } catch (e) {
    return e instanceof Error ? e : new Error(e);
  }
}

// node_modules/es-toolkit/dist/compat/function/before.mjs
function before2(n2, func) {
  if (typeof func !== "function") {
    throw new TypeError("Expected a function");
  }
  let result2;
  n2 = toInteger(n2);
  return function(...args) {
    if (--n2 > 0) {
      result2 = func.apply(this, args);
    }
    if (n2 <= 1 && func) {
      func = void 0;
    }
    return result2;
  };
}

// node_modules/es-toolkit/dist/compat/function/bind.mjs
function bind(func, thisObj, ...partialArgs) {
  const bound = function(...providedArgs) {
    const args = [];
    let startIndex = 0;
    for (let i = 0; i < partialArgs.length; i++) {
      const arg = partialArgs[i];
      if (arg === bind.placeholder) {
        args.push(providedArgs[startIndex++]);
      } else {
        args.push(arg);
      }
    }
    for (let i = startIndex; i < providedArgs.length; i++) {
      args.push(providedArgs[i]);
    }
    if (this instanceof bound) {
      return new func(...args);
    }
    return func.apply(thisObj, args);
  };
  return bound;
}
var bindPlaceholder = Symbol("bind.placeholder");
bind.placeholder = bindPlaceholder;

// node_modules/es-toolkit/dist/compat/function/bindKey.mjs
function bindKey(object, key, ...partialArgs) {
  const bound = function(...providedArgs) {
    const args = [];
    let startIndex = 0;
    for (let i = 0; i < partialArgs.length; i++) {
      const arg = partialArgs[i];
      if (arg === bindKey.placeholder) {
        args.push(providedArgs[startIndex++]);
      } else {
        args.push(arg);
      }
    }
    for (let i = startIndex; i < providedArgs.length; i++) {
      args.push(providedArgs[i]);
    }
    if (this instanceof bound) {
      return new object[key](...args);
    }
    return object[key].apply(object, args);
  };
  return bound;
}
var bindKeyPlaceholder = Symbol("bindKey.placeholder");
bindKey.placeholder = bindKeyPlaceholder;

// node_modules/es-toolkit/dist/compat/function/curry.mjs
function curry2(func, arity = func.length, guard) {
  arity = guard ? func.length : arity;
  arity = Number.parseInt(arity, 10);
  if (Number.isNaN(arity) || arity < 1) {
    arity = 0;
  }
  const wrapper = function(...partialArgs) {
    const holders = partialArgs.filter((item) => item === curry2.placeholder);
    const length = partialArgs.length - holders.length;
    if (length < arity) {
      return makeCurry(func, arity - length, partialArgs);
    }
    if (this instanceof wrapper) {
      return new func(...partialArgs);
    }
    return func.apply(this, partialArgs);
  };
  wrapper.placeholder = curryPlaceholder;
  return wrapper;
}
function makeCurry(func, arity, partialArgs) {
  function wrapper(...providedArgs) {
    const holders = providedArgs.filter((item) => item === curry2.placeholder);
    const length = providedArgs.length - holders.length;
    providedArgs = composeArgs(providedArgs, partialArgs);
    if (length < arity) {
      return makeCurry(func, arity - length, providedArgs);
    }
    if (this instanceof wrapper) {
      return new func(...providedArgs);
    }
    return func.apply(this, providedArgs);
  }
  wrapper.placeholder = curryPlaceholder;
  return wrapper;
}
function composeArgs(providedArgs, partialArgs) {
  const args = [];
  let startIndex = 0;
  for (let i = 0; i < partialArgs.length; i++) {
    const arg = partialArgs[i];
    if (arg === curry2.placeholder && startIndex < providedArgs.length) {
      args.push(providedArgs[startIndex++]);
    } else {
      args.push(arg);
    }
  }
  for (let i = startIndex; i < providedArgs.length; i++) {
    args.push(providedArgs[i]);
  }
  return args;
}
var curryPlaceholder = Symbol("curry.placeholder");
curry2.placeholder = curryPlaceholder;

// node_modules/es-toolkit/dist/compat/function/curryRight.mjs
function curryRight2(func, arity = func.length, guard) {
  arity = guard ? func.length : arity;
  arity = Number.parseInt(arity, 10);
  if (Number.isNaN(arity) || arity < 1) {
    arity = 0;
  }
  const wrapper = function(...partialArgs) {
    const holders = partialArgs.filter((item) => item === curryRight2.placeholder);
    const length = partialArgs.length - holders.length;
    if (length < arity) {
      return makeCurryRight(func, arity - length, partialArgs);
    }
    if (this instanceof wrapper) {
      return new func(...partialArgs);
    }
    return func.apply(this, partialArgs);
  };
  wrapper.placeholder = curryRightPlaceholder;
  return wrapper;
}
function makeCurryRight(func, arity, partialArgs) {
  function wrapper(...providedArgs) {
    const holders = providedArgs.filter((item) => item === curryRight2.placeholder);
    const length = providedArgs.length - holders.length;
    providedArgs = composeArgs2(providedArgs, partialArgs);
    if (length < arity) {
      return makeCurryRight(func, arity - length, providedArgs);
    }
    if (this instanceof wrapper) {
      return new func(...providedArgs);
    }
    return func.apply(this, providedArgs);
  }
  wrapper.placeholder = curryRightPlaceholder;
  return wrapper;
}
function composeArgs2(providedArgs, partialArgs) {
  const placeholderLength = partialArgs.filter((arg) => arg === curryRight2.placeholder).length;
  const rangeLength = Math.max(providedArgs.length - placeholderLength, 0);
  const args = [];
  let providedIndex = 0;
  for (let i = 0; i < rangeLength; i++) {
    args.push(providedArgs[providedIndex++]);
  }
  for (let i = 0; i < partialArgs.length; i++) {
    const arg = partialArgs[i];
    if (arg === curryRight2.placeholder) {
      if (providedIndex < providedArgs.length) {
        args.push(providedArgs[providedIndex++]);
      } else {
        args.push(arg);
      }
    } else {
      args.push(arg);
    }
  }
  return args;
}
var curryRightPlaceholder = Symbol("curryRight.placeholder");
curryRight2.placeholder = curryRightPlaceholder;

// node_modules/es-toolkit/dist/compat/function/debounce.mjs
function debounce2(func, debounceMs = 0, options = {}) {
  if (typeof options !== "object") {
    options = {};
  }
  const { signal, leading = false, trailing = true, maxWait } = options;
  const edges = Array(2);
  if (leading) {
    edges[0] = "leading";
  }
  if (trailing) {
    edges[1] = "trailing";
  }
  let result2 = void 0;
  let pendingAt = null;
  const _debounced = debounce(function(...args) {
    result2 = func.apply(this, args);
    pendingAt = null;
  }, debounceMs, { signal, edges });
  const debounced = function(...args) {
    if (maxWait != null) {
      if (pendingAt === null) {
        pendingAt = Date.now();
      }
      if (Date.now() - pendingAt >= maxWait) {
        result2 = func.apply(this, args);
        pendingAt = Date.now();
        _debounced.cancel();
        _debounced.schedule();
        return result2;
      }
    }
    _debounced.apply(this, args);
    return result2;
  };
  const flush = () => {
    _debounced.flush();
    return result2;
  };
  debounced.cancel = _debounced.cancel;
  debounced.flush = flush;
  return debounced;
}

// node_modules/es-toolkit/dist/compat/function/defer.mjs
function defer(func, ...args) {
  if (typeof func !== "function") {
    throw new TypeError("Expected a function");
  }
  return setTimeout(func, 1, ...args);
}

// node_modules/es-toolkit/dist/compat/function/delay.mjs
function delay2(func, wait, ...args) {
  if (typeof func !== "function") {
    throw new TypeError("Expected a function");
  }
  return setTimeout(func, toNumber(wait) || 0, ...args);
}

// node_modules/es-toolkit/dist/compat/function/flip.mjs
function flip(func) {
  return function(...args) {
    return func.apply(this, args.reverse());
  };
}

// node_modules/es-toolkit/dist/compat/function/flow.mjs
function flow2(...funcs) {
  const flattenFuncs = flatten(funcs, 1);
  if (flattenFuncs.some((func) => typeof func !== "function")) {
    throw new TypeError("Expected a function");
  }
  return flow(...flattenFuncs);
}

// node_modules/es-toolkit/dist/compat/function/flowRight.mjs
function flowRight2(...funcs) {
  const flattenFuncs = flatten(funcs, 1);
  if (flattenFuncs.some((func) => typeof func !== "function")) {
    throw new TypeError("Expected a function");
  }
  return flowRight(...flattenFuncs);
}

// node_modules/es-toolkit/dist/compat/function/memoize.mjs
function memoize2(func, resolver) {
  if (typeof func !== "function" || resolver != null && typeof resolver !== "function") {
    throw new TypeError("Expected a function");
  }
  const memoized = function(...args) {
    const key = resolver ? resolver.apply(this, args) : args[0];
    const cache = memoized.cache;
    if (cache.has(key)) {
      return cache.get(key);
    }
    const result2 = func.apply(this, args);
    memoized.cache = cache.set(key, result2) || cache;
    return result2;
  };
  const CacheConstructor = memoize2.Cache || Map;
  memoized.cache = new CacheConstructor();
  return memoized;
}
memoize2.Cache = Map;

// node_modules/es-toolkit/dist/compat/function/nthArg.mjs
function nthArg(n2 = 0) {
  return function(...args) {
    return args.at(toInteger(n2));
  };
}

// node_modules/es-toolkit/dist/compat/function/partial.mjs
function partial2(func, ...partialArgs) {
  return partialImpl(func, partial2.placeholder, ...partialArgs);
}
partial2.placeholder = Symbol("compat.partial.placeholder");

// node_modules/es-toolkit/dist/compat/function/partialRight.mjs
function partialRight2(func, ...partialArgs) {
  return partialRightImpl(func, partialRight2.placeholder, ...partialArgs);
}
partialRight2.placeholder = Symbol("compat.partialRight.placeholder");

// node_modules/es-toolkit/dist/compat/function/rearg.mjs
function rearg(func, ...indices) {
  const flattenIndices = flatten2(indices);
  return function(...args) {
    const reorderedArgs = flattenIndices.map((i) => args[i]).slice(0, args.length);
    for (let i = reorderedArgs.length; i < args.length; i++) {
      reorderedArgs.push(args[i]);
    }
    return func.apply(this, reorderedArgs);
  };
}

// node_modules/es-toolkit/dist/compat/function/rest.mjs
function rest2(func, start = func.length - 1) {
  start = Number.parseInt(start, 10);
  if (Number.isNaN(start) || start < 0) {
    start = func.length - 1;
  }
  return rest(func, start);
}

// node_modules/es-toolkit/dist/compat/function/spread.mjs
function spread2(func, argsIndex = 0) {
  argsIndex = Number.parseInt(argsIndex, 10);
  if (Number.isNaN(argsIndex) || argsIndex < 0) {
    argsIndex = 0;
  }
  return function(...args) {
    const array = args[argsIndex];
    const params = args.slice(0, argsIndex);
    if (array) {
      params.push(...array);
    }
    return func.apply(this, params);
  };
}

// node_modules/es-toolkit/dist/compat/function/throttle.mjs
function throttle2(func, throttleMs = 0, options = {}) {
  if (typeof options !== "object") {
    options = {};
  }
  const { leading = true, trailing = true, signal } = options;
  return debounce2(func, throttleMs, {
    leading,
    trailing,
    signal,
    maxWait: throttleMs
  });
}

// node_modules/es-toolkit/dist/compat/function/wrap.mjs
function wrap(value, wrapper) {
  return function(...args) {
    const wrapFn = isFunction(wrapper) ? wrapper : identity;
    return wrapFn.apply(this, [value, ...args]);
  };
}

// node_modules/es-toolkit/dist/compat/util/toString.mjs
function toString(value) {
  if (value == null) {
    return "";
  }
  if (typeof value === "string") {
    return value;
  }
  if (Array.isArray(value)) {
    return value.map(toString).join(",");
  }
  const result2 = String(value);
  if (result2 === "0" && Object.is(Number(value), -0)) {
    return "-0";
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/math/add.mjs
function add(value, other) {
  if (value === void 0 && other === void 0) {
    return 0;
  }
  if (value === void 0 || other === void 0) {
    return value ?? other;
  }
  if (typeof value === "string" || typeof other === "string") {
    value = toString(value);
    other = toString(other);
  } else {
    value = toNumber(value);
    other = toNumber(other);
  }
  return value + other;
}

// node_modules/es-toolkit/dist/compat/_internal/decimalAdjust.mjs
function decimalAdjust(type, number, precision = 0) {
  number = Number(number);
  if (Object.is(number, -0)) {
    number = "-0";
  }
  precision = Math.min(Number.parseInt(precision, 10), 292);
  if (precision) {
    const [magnitude, exponent = 0] = number.toString().split("e");
    let adjustedValue = Math[type](Number(`${magnitude}e${Number(exponent) + precision}`));
    if (Object.is(adjustedValue, -0)) {
      adjustedValue = "-0";
    }
    const [newMagnitude, newExponent = 0] = adjustedValue.toString().split("e");
    return Number(`${newMagnitude}e${Number(newExponent) - precision}`);
  }
  return Math[type](Number(number));
}

// node_modules/es-toolkit/dist/compat/math/ceil.mjs
function ceil(number, precision = 0) {
  return decimalAdjust("ceil", number, precision);
}

// node_modules/es-toolkit/dist/compat/math/divide.mjs
function divide(value, other) {
  if (value === void 0 && other === void 0) {
    return 1;
  }
  if (value === void 0 || other === void 0) {
    return value ?? other;
  }
  if (typeof value === "string" || typeof other === "string") {
    value = toString(value);
    other = toString(other);
  } else {
    value = toNumber(value);
    other = toNumber(other);
  }
  return value / other;
}

// node_modules/es-toolkit/dist/compat/math/floor.mjs
function floor(number, precision = 0) {
  return decimalAdjust("floor", number, precision);
}

// node_modules/es-toolkit/dist/compat/math/inRange.mjs
function inRange2(value, minimum, maximum) {
  if (!minimum) {
    minimum = 0;
  }
  if (maximum != null && !maximum) {
    maximum = 0;
  }
  if (minimum != null && typeof minimum !== "number") {
    minimum = Number(minimum);
  }
  if (maximum == null && minimum === 0) {
    return false;
  }
  if (maximum != null && typeof maximum !== "number") {
    maximum = Number(maximum);
  }
  if (maximum != null && minimum > maximum) {
    [minimum, maximum] = [maximum, minimum];
  }
  if (minimum === maximum) {
    return false;
  }
  return inRange(value, minimum, maximum);
}

// node_modules/es-toolkit/dist/compat/math/max.mjs
function max(items) {
  if (!items || items.length === 0) {
    return void 0;
  }
  let maxResult = void 0;
  for (let i = 0; i < items.length; i++) {
    const current = items[i];
    if (current == null || Number.isNaN(current) || typeof current === "symbol") {
      continue;
    }
    if (maxResult === void 0 || current > maxResult) {
      maxResult = current;
    }
  }
  return maxResult;
}

// node_modules/es-toolkit/dist/compat/math/maxBy.mjs
function maxBy2(items, iteratee$1) {
  if (items == null) {
    return void 0;
  }
  return maxBy(Array.from(items), iteratee(iteratee$1));
}

// node_modules/es-toolkit/dist/compat/math/sumBy.mjs
function sumBy2(array, iteratee$1) {
  if (!array || !array.length) {
    return 0;
  }
  if (iteratee$1 != null) {
    iteratee$1 = iteratee(iteratee$1);
  }
  let result2 = void 0;
  for (let i = 0; i < array.length; i++) {
    const current = iteratee$1 ? iteratee$1(array[i]) : array[i];
    if (current !== void 0) {
      if (result2 === void 0) {
        result2 = current;
      } else {
        result2 += current;
      }
    }
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/math/sum.mjs
function sum2(array) {
  return sumBy2(array);
}

// node_modules/es-toolkit/dist/compat/math/mean.mjs
function mean2(nums) {
  const length = nums ? nums.length : 0;
  return length === 0 ? NaN : sum2(nums) / length;
}

// node_modules/es-toolkit/dist/compat/math/meanBy.mjs
function meanBy2(items, iteratee$1) {
  if (items == null) {
    return NaN;
  }
  return meanBy(Array.from(items), iteratee(iteratee$1));
}

// node_modules/es-toolkit/dist/compat/math/min.mjs
function min(items) {
  if (!items || items.length === 0) {
    return void 0;
  }
  let minResult = void 0;
  for (let i = 0; i < items.length; i++) {
    const current = items[i];
    if (current == null || Number.isNaN(current) || typeof current === "symbol") {
      continue;
    }
    if (minResult === void 0 || current < minResult) {
      minResult = current;
    }
  }
  return minResult;
}

// node_modules/es-toolkit/dist/compat/math/minBy.mjs
function minBy2(items, iteratee$1) {
  if (items == null) {
    return void 0;
  }
  return minBy(Array.from(items), iteratee(iteratee$1));
}

// node_modules/es-toolkit/dist/compat/math/multiply.mjs
function multiply(value, other) {
  if (value === void 0 && other === void 0) {
    return 1;
  }
  if (value === void 0 || other === void 0) {
    return value ?? other;
  }
  if (typeof value === "string" || typeof other === "string") {
    value = toString(value);
    other = toString(other);
  } else {
    value = toNumber(value);
    other = toNumber(other);
  }
  return value * other;
}

// node_modules/es-toolkit/dist/compat/math/parseInt.mjs
function parseInt2(string, radix = 0, guard) {
  if (guard) {
    radix = 0;
  }
  return Number.parseInt(string, radix);
}

// node_modules/es-toolkit/dist/compat/math/random.mjs
function random2(...args) {
  let minimum = 0;
  let maximum = 1;
  let floating = false;
  switch (args.length) {
    case 1: {
      if (typeof args[0] === "boolean") {
        floating = args[0];
      } else {
        maximum = args[0];
      }
      break;
    }
    case 2: {
      if (typeof args[1] === "boolean") {
        maximum = args[0];
        floating = args[1];
      } else {
        minimum = args[0];
        maximum = args[1];
      }
    }
    case 3: {
      if (typeof args[2] === "object" && args[2] != null && args[2][args[1]] === args[0]) {
        minimum = 0;
        maximum = args[0];
        floating = false;
      } else {
        minimum = args[0];
        maximum = args[1];
        floating = args[2];
      }
    }
  }
  if (typeof minimum !== "number") {
    minimum = Number(minimum);
  }
  if (typeof maximum !== "number") {
    minimum = Number(maximum);
  }
  if (!minimum) {
    minimum = 0;
  }
  if (!maximum) {
    maximum = 0;
  }
  if (minimum > maximum) {
    [minimum, maximum] = [maximum, minimum];
  }
  minimum = clamp2(minimum, -Number.MAX_SAFE_INTEGER, Number.MAX_SAFE_INTEGER);
  maximum = clamp2(maximum, -Number.MAX_SAFE_INTEGER, Number.MAX_SAFE_INTEGER);
  if (minimum === maximum) {
    return minimum;
  }
  if (floating) {
    return random(minimum, maximum + 1);
  } else {
    return randomInt(minimum, maximum + 1);
  }
}

// node_modules/es-toolkit/dist/compat/math/range.mjs
function range2(start, end, step) {
  if (step && typeof step !== "number" && isIterateeCall(start, end, step)) {
    end = step = void 0;
  }
  start = toFinite(start);
  if (end === void 0) {
    end = start;
    start = 0;
  } else {
    end = toFinite(end);
  }
  step = step === void 0 ? start < end ? 1 : -1 : toFinite(step);
  const length = Math.max(Math.ceil((end - start) / (step || 1)), 0);
  const result2 = new Array(length);
  for (let index = 0; index < length; index++) {
    result2[index] = start;
    start += step;
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/math/rangeRight.mjs
function rangeRight2(start, end, step) {
  if (step && typeof step !== "number" && isIterateeCall(start, end, step)) {
    end = step = void 0;
  }
  start = toFinite(start);
  if (end === void 0) {
    end = start;
    start = 0;
  } else {
    end = toFinite(end);
  }
  step = step === void 0 ? start < end ? 1 : -1 : toFinite(step);
  const length = Math.max(Math.ceil((end - start) / (step || 1)), 0);
  const result2 = new Array(length);
  for (let index = length - 1; index >= 0; index--) {
    result2[index] = start;
    start += step;
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/math/round.mjs
function round2(number, precision = 0) {
  return decimalAdjust("round", number, precision);
}

// node_modules/es-toolkit/dist/compat/math/subtract.mjs
function subtract(value, other) {
  if (value === void 0 && other === void 0) {
    return 0;
  }
  if (value === void 0 || other === void 0) {
    return value ?? other;
  }
  if (typeof value === "string" || typeof other === "string") {
    value = toString(value);
    other = toString(other);
  } else {
    value = toNumber(value);
    other = toNumber(other);
  }
  return value - other;
}

// node_modules/es-toolkit/dist/compat/_internal/isPrototype.mjs
function isPrototype(value) {
  const constructor = value == null ? void 0 : value.constructor;
  const prototype = typeof constructor === "function" ? constructor.prototype : Object.prototype;
  return value === prototype;
}

// node_modules/es-toolkit/dist/compat/predicate/isTypedArray.mjs
function isTypedArray2(x3) {
  return isTypedArray(x3);
}

// node_modules/es-toolkit/dist/compat/util/times.mjs
function times(n2, getValue) {
  n2 = toInteger(n2);
  if (n2 < 1 || !Number.isSafeInteger(n2)) {
    return [];
  }
  const result2 = new Array(n2);
  for (let i = 0; i < n2; i++) {
    result2[i] = typeof getValue === "function" ? getValue(i) : i;
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/object/keys.mjs
function keys(object) {
  if (isArrayLike(object)) {
    return arrayLikeKeys(object);
  }
  const result2 = Object.keys(Object(object));
  if (!isPrototype(object)) {
    return result2;
  }
  return result2.filter((key) => key !== "constructor");
}
function arrayLikeKeys(object) {
  const indices = times(object.length, (index) => `${index}`);
  const filteredKeys = new Set(indices);
  if (isBuffer(object)) {
    filteredKeys.add("offset");
    filteredKeys.add("parent");
  }
  if (isTypedArray2(object)) {
    filteredKeys.add("buffer");
    filteredKeys.add("byteLength");
    filteredKeys.add("byteOffset");
  }
  return [...indices, ...Object.keys(object).filter((key) => !filteredKeys.has(key))];
}

// node_modules/es-toolkit/dist/compat/object/assign.mjs
function assign(object, ...sources) {
  for (let i = 0; i < sources.length; i++) {
    assignImpl(object, sources[i]);
  }
  return object;
}
function assignImpl(object, source) {
  const keys$1 = keys(source);
  for (let i = 0; i < keys$1.length; i++) {
    const key = keys$1[i];
    if (!(key in object) || !eq(object[key], source[key])) {
      object[key] = source[key];
    }
  }
}

// node_modules/es-toolkit/dist/compat/object/keysIn.mjs
function keysIn(object) {
  if (object == null) {
    return [];
  }
  switch (typeof object) {
    case "object":
    case "function": {
      if (isArrayLike(object)) {
        return arrayLikeKeysIn(object);
      }
      if (isPrototype(object)) {
        return prototypeKeysIn(object);
      }
      return keysInImpl(object);
    }
    default: {
      return keysInImpl(Object(object));
    }
  }
}
function keysInImpl(object) {
  const result2 = [];
  for (const key in object) {
    result2.push(key);
  }
  return result2;
}
function prototypeKeysIn(object) {
  const keys2 = keysInImpl(object);
  return keys2.filter((key) => key !== "constructor");
}
function arrayLikeKeysIn(object) {
  const indices = times(object.length, (index) => `${index}`);
  const filteredKeys = new Set(indices);
  if (isBuffer(object)) {
    filteredKeys.add("offset");
    filteredKeys.add("parent");
  }
  if (isTypedArray2(object)) {
    filteredKeys.add("buffer");
    filteredKeys.add("byteLength");
    filteredKeys.add("byteOffset");
  }
  return [...indices, ...keysInImpl(object).filter((key) => !filteredKeys.has(key))];
}

// node_modules/es-toolkit/dist/compat/object/assignIn.mjs
function assignIn(object, ...sources) {
  for (let i = 0; i < sources.length; i++) {
    assignInImpl(object, sources[i]);
  }
  return object;
}
function assignInImpl(object, source) {
  const keys2 = keysIn(source);
  for (let i = 0; i < keys2.length; i++) {
    const key = keys2[i];
    if (!(key in object) || !eq(object[key], source[key])) {
      object[key] = source[key];
    }
  }
}

// node_modules/es-toolkit/dist/compat/object/assignInWith.mjs
function assignInWith(object, ...sources) {
  let getValueToAssign = sources[sources.length - 1];
  if (typeof getValueToAssign === "function") {
    sources.pop();
  } else {
    getValueToAssign = void 0;
  }
  for (let i = 0; i < sources.length; i++) {
    assignInWithImpl(object, sources[i], getValueToAssign);
  }
  return object;
}
function assignInWithImpl(object, source, getValueToAssign) {
  const keys2 = keysIn(source);
  for (let i = 0; i < keys2.length; i++) {
    const key = keys2[i];
    const objValue = object[key];
    const srcValue = source[key];
    const newValue = (getValueToAssign == null ? void 0 : getValueToAssign(objValue, srcValue, key, object, source)) ?? srcValue;
    if (!(key in object) || !eq(objValue, newValue)) {
      object[key] = newValue;
    }
  }
}

// node_modules/es-toolkit/dist/compat/object/assignWith.mjs
function assignWith(object, ...sources) {
  let getValueToAssign = sources[sources.length - 1];
  if (typeof getValueToAssign === "function") {
    sources.pop();
  } else {
    getValueToAssign = void 0;
  }
  for (let i = 0; i < sources.length; i++) {
    assignWithImpl(object, sources[i], getValueToAssign);
  }
  return object;
}
function assignWithImpl(object, source, getValueToAssign) {
  const keys$1 = keys(source);
  for (let i = 0; i < keys$1.length; i++) {
    const key = keys$1[i];
    const objValue = object[key];
    const srcValue = source[key];
    const newValue = (getValueToAssign == null ? void 0 : getValueToAssign(objValue, srcValue, key, object, source)) ?? srcValue;
    if (!(key in object) || !eq(objValue, newValue)) {
      object[key] = newValue;
    }
  }
}

// node_modules/es-toolkit/dist/compat/object/clone.mjs
function clone2(obj) {
  if (isPrimitive(obj)) {
    return obj;
  }
  const tag = getTag(obj);
  if (!isCloneableObject2(obj)) {
    return {};
  }
  if (isArray(obj)) {
    const result3 = Array.from(obj);
    if (obj.length > 0 && typeof obj[0] === "string" && Object.hasOwn(obj, "index")) {
      result3.index = obj.index;
      result3.input = obj.input;
    }
    return result3;
  }
  if (isTypedArray2(obj)) {
    const typedArray = obj;
    const Ctor = typedArray.constructor;
    return new Ctor(typedArray.buffer, typedArray.byteOffset, typedArray.length);
  }
  if (tag === arrayBufferTag) {
    return new ArrayBuffer(obj.byteLength);
  }
  if (tag === dataViewTag) {
    const dataView = obj;
    const buffer = dataView.buffer;
    const byteOffset = dataView.byteOffset;
    const byteLength = dataView.byteLength;
    const clonedBuffer = new ArrayBuffer(byteLength);
    const srcView = new Uint8Array(buffer, byteOffset, byteLength);
    const destView = new Uint8Array(clonedBuffer);
    destView.set(srcView);
    return new DataView(clonedBuffer);
  }
  if (tag === booleanTag || tag === numberTag || tag === stringTag) {
    const Ctor = obj.constructor;
    const clone3 = new Ctor(obj.valueOf());
    if (tag === stringTag) {
      cloneStringObjectProperties(clone3, obj);
    } else {
      copyOwnProperties(clone3, obj);
    }
    return clone3;
  }
  if (tag === dateTag) {
    return new Date(Number(obj));
  }
  if (tag === regexpTag) {
    const regExp = obj;
    const clone3 = new RegExp(regExp.source, regExp.flags);
    clone3.lastIndex = regExp.lastIndex;
    return clone3;
  }
  if (tag === symbolTag) {
    return Object(Symbol.prototype.valueOf.call(obj));
  }
  if (tag === mapTag) {
    const map2 = obj;
    const result3 = /* @__PURE__ */ new Map();
    map2.forEach((obj2, key) => {
      result3.set(key, obj2);
    });
    return result3;
  }
  if (tag === setTag) {
    const set2 = obj;
    const result3 = /* @__PURE__ */ new Set();
    set2.forEach((obj2) => {
      result3.add(obj2);
    });
    return result3;
  }
  if (tag === argumentsTag) {
    const args = obj;
    const result3 = {};
    copyOwnProperties(result3, args);
    result3.length = args.length;
    result3[Symbol.iterator] = args[Symbol.iterator];
    return result3;
  }
  const result2 = {};
  copyPrototype(result2, obj);
  copyOwnProperties(result2, obj);
  copySymbolProperties(result2, obj);
  return result2;
}
function isCloneableObject2(object) {
  switch (getTag(object)) {
    case argumentsTag:
    case arrayTag:
    case arrayBufferTag:
    case dataViewTag:
    case booleanTag:
    case dateTag:
    case float32ArrayTag:
    case float64ArrayTag:
    case int8ArrayTag:
    case int16ArrayTag:
    case int32ArrayTag:
    case mapTag:
    case numberTag:
    case objectTag:
    case regexpTag:
    case setTag:
    case stringTag:
    case symbolTag:
    case uint8ArrayTag:
    case uint8ClampedArrayTag:
    case uint16ArrayTag:
    case uint32ArrayTag: {
      return true;
    }
    default: {
      return false;
    }
  }
}
function copyOwnProperties(target, source) {
  for (const key in source) {
    if (Object.hasOwn(source, key)) {
      target[key] = source[key];
    }
  }
}
function copySymbolProperties(target, source) {
  const symbols = Object.getOwnPropertySymbols(source);
  for (let i = 0; i < symbols.length; i++) {
    const symbol = symbols[i];
    if (Object.prototype.propertyIsEnumerable.call(source, symbol)) {
      target[symbol] = source[symbol];
    }
  }
}
function cloneStringObjectProperties(target, source) {
  const stringLength = source.valueOf().length;
  for (const key in source) {
    if (Object.hasOwn(source, key) && (Number.isNaN(Number(key)) || Number(key) >= stringLength)) {
      target[key] = source[key];
    }
  }
}
function copyPrototype(target, source) {
  const proto = Object.getPrototypeOf(source);
  if (proto !== null) {
    const Ctor = source.constructor;
    if (typeof Ctor === "function") {
      Object.setPrototypeOf(target, proto);
    }
  }
}

// node_modules/es-toolkit/dist/compat/object/cloneWith.mjs
function cloneWith(value, customizer) {
  if (!customizer) {
    return clone2(value);
  }
  const result2 = customizer(value);
  if (result2 !== void 0) {
    return result2;
  }
  return clone2(value);
}

// node_modules/es-toolkit/dist/compat/object/create.mjs
function create(prototype, properties) {
  const proto = isObject(prototype) ? Object.create(prototype) : {};
  if (properties != null) {
    const propsKeys = keys(properties);
    for (let i = 0; i < propsKeys.length; i++) {
      const key = propsKeys[i];
      const propsValue = properties[key];
      assignValue(proto, key, propsValue);
    }
  }
  return proto;
}

// node_modules/es-toolkit/dist/compat/object/defaults.mjs
function defaults(object, ...sources) {
  object = Object(object);
  const objectProto = Object.prototype;
  let length = sources.length;
  const guard = length > 2 ? sources[2] : void 0;
  if (guard && isIterateeCall(sources[0], sources[1], guard)) {
    length = 1;
  }
  for (let i = 0; i < length; i++) {
    const source = sources[i];
    const keys2 = Object.keys(source);
    for (let j2 = 0; j2 < keys2.length; j2++) {
      const key = keys2[j2];
      const value = object[key];
      if (value === void 0 || !Object.hasOwn(object, key) && eq(value, objectProto[key])) {
        object[key] = source[key];
      }
    }
  }
  return object;
}

// node_modules/es-toolkit/dist/compat/object/findKey.mjs
function findKey2(obj, predicate) {
  if (!isObject(obj)) {
    return void 0;
  }
  return findKeyImpl(obj, predicate);
}
function findKeyImpl(obj, predicate) {
  if (typeof predicate === "function") {
    return findKey(obj, predicate);
  }
  if (typeof predicate === "object") {
    if (Array.isArray(predicate)) {
      const key = predicate[0];
      const value = predicate[1];
      return findKey(obj, matchesProperty(key, value));
    }
    return findKey(obj, matches(predicate));
  }
  if (typeof predicate === "string") {
    return findKey(obj, property(predicate));
  }
}

// node_modules/es-toolkit/dist/compat/object/forIn.mjs
function forIn(object, iteratee2 = identity) {
  if (object == null) {
    return object;
  }
  for (const key in object) {
    const result2 = iteratee2(object[key], key, object);
    if (result2 === false) {
      break;
    }
  }
  return object;
}

// node_modules/es-toolkit/dist/compat/object/forInRight.mjs
function forInRight(object, iteratee2 = identity) {
  if (object == null) {
    return object;
  }
  const keys2 = [];
  for (const key in object) {
    keys2.push(key);
  }
  for (let i = keys2.length - 1; i >= 0; i--) {
    const key = keys2[i];
    const result2 = iteratee2(object[key], key, object);
    if (result2 === false) {
      break;
    }
  }
  return object;
}

// node_modules/es-toolkit/dist/compat/object/forOwn.mjs
function forOwn(object, iteratee2 = identity) {
  if (object == null) {
    return object;
  }
  const iterable = Object(object);
  const keys$1 = keys(object);
  for (let i = 0; i < keys$1.length; ++i) {
    const key = keys$1[i];
    if (iteratee2(iterable[key], key, iterable) === false) {
      break;
    }
  }
  return object;
}

// node_modules/es-toolkit/dist/compat/object/forOwnRight.mjs
function forOwnRight(object, iteratee2 = identity) {
  if (object == null) {
    return object;
  }
  const iterable = Object(object);
  const keys$1 = keys(object);
  for (let i = keys$1.length - 1; i >= 0; --i) {
    const key = keys$1[i];
    if (iteratee2(iterable[key], key, iterable) === false) {
      break;
    }
  }
  return object;
}

// node_modules/es-toolkit/dist/compat/object/fromPairs.mjs
function fromPairs(pairs) {
  if (!isArrayLike(pairs) && !(pairs instanceof Map)) {
    return {};
  }
  const result2 = {};
  for (const [key, value] of pairs) {
    result2[key] = value;
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/object/functions.mjs
function functions(object) {
  if (object == null) {
    return [];
  }
  return keys(object).filter((key) => typeof object[key] === "function");
}

// node_modules/es-toolkit/dist/compat/object/functionsIn.mjs
function functionsIn(object) {
  if (object == null) {
    return [];
  }
  const result2 = [];
  for (const key in object) {
    if (isFunction(object[key])) {
      result2.push(key);
    }
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/object/hasIn.mjs
function hasIn(object, path) {
  let resolvedPath;
  if (Array.isArray(path)) {
    resolvedPath = path;
  } else if (typeof path === "string" && isDeepKey(path) && (object == null ? void 0 : object[path]) == null) {
    resolvedPath = toPath(path);
  } else {
    resolvedPath = [path];
  }
  if (resolvedPath.length === 0) {
    return false;
  }
  let current = object;
  for (let i = 0; i < resolvedPath.length; i++) {
    const key = resolvedPath[i];
    if (current == null || !(key in Object(current))) {
      const isSparseIndex = (Array.isArray(current) || isArguments(current)) && isIndex(key) && key < current.length;
      if (!isSparseIndex) {
        return false;
      }
    }
    current = current[key];
  }
  return true;
}

// node_modules/es-toolkit/dist/compat/object/invertBy.mjs
function invertBy(object, iteratee2) {
  const result2 = {};
  if (isNil(object)) {
    return result2;
  }
  if (iteratee2 == null) {
    iteratee2 = identity;
  }
  const keys2 = Object.keys(object);
  for (let i = 0; i < keys2.length; i++) {
    const key = keys2[i];
    const value = object[key];
    const valueStr = iteratee2(value);
    if (Array.isArray(result2[valueStr])) {
      result2[valueStr].push(key);
    } else {
      result2[valueStr] = [key];
    }
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/predicate/isNative.mjs
var functionToString = Function.prototype.toString;
var REGEXP_SYNTAX_CHARS = /[\\^$.*+?()[\]{}|]/g;
var IS_NATIVE_FUNCTION_REGEXP = RegExp(`^${functionToString.call(Object.prototype.hasOwnProperty).replace(REGEXP_SYNTAX_CHARS, "\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g, "$1.*?")}$`);
function isNative(value) {
  if (typeof value !== "function") {
    return false;
  }
  if ((globalThis == null ? void 0 : globalThis["__core-js_shared__"]) != null) {
    throw new Error("Unsupported core-js use. Try https://npms.io/search?q=ponyfill.");
  }
  return IS_NATIVE_FUNCTION_REGEXP.test(functionToString.call(value));
}

// node_modules/es-toolkit/dist/compat/object/mapKeys.mjs
function mapKeys2(object, getNewKey) {
  getNewKey = getNewKey ?? identity;
  switch (typeof getNewKey) {
    case "string":
    case "symbol":
    case "number":
    case "object": {
      return mapKeys(object, property(getNewKey));
    }
    case "function": {
      return mapKeys(object, getNewKey);
    }
  }
}

// node_modules/es-toolkit/dist/compat/object/mapValues.mjs
function mapValues2(object, getNewValue) {
  getNewValue = getNewValue ?? identity;
  switch (typeof getNewValue) {
    case "string":
    case "symbol":
    case "number":
    case "object": {
      return mapValues(object, property(getNewValue));
    }
    case "function": {
      return mapValues(object, getNewValue);
    }
  }
}

// node_modules/es-toolkit/dist/compat/object/mergeWith.mjs
function mergeWith2(object, ...otherArgs) {
  const sources = otherArgs.slice(0, -1);
  const merge3 = otherArgs[otherArgs.length - 1];
  let result2 = object;
  for (let i = 0; i < sources.length; i++) {
    const source = sources[i];
    result2 = mergeWithDeep(result2, source, merge3, /* @__PURE__ */ new Map());
  }
  return result2;
}
function mergeWithDeep(target, source, merge3, stack) {
  if (isPrimitive(target)) {
    target = Object(target);
  }
  if (source == null || typeof source !== "object") {
    return target;
  }
  if (stack.has(source)) {
    return clone(stack.get(source));
  }
  stack.set(source, target);
  if (Array.isArray(source)) {
    source = source.slice();
    for (let i = 0; i < source.length; i++) {
      source[i] = source[i] ?? void 0;
    }
  }
  const sourceKeys = [...Object.keys(source), ...getSymbols(source)];
  for (let i = 0; i < sourceKeys.length; i++) {
    const key = sourceKeys[i];
    let sourceValue = source[key];
    let targetValue = target[key];
    if (isArguments(sourceValue)) {
      sourceValue = { ...sourceValue };
    }
    if (isArguments(targetValue)) {
      targetValue = { ...targetValue };
    }
    if (typeof Buffer !== "undefined" && Buffer.isBuffer(sourceValue)) {
      sourceValue = cloneDeep2(sourceValue);
    }
    if (Array.isArray(sourceValue)) {
      if (typeof targetValue === "object" && targetValue != null) {
        const cloned = [];
        const targetKeys = Reflect.ownKeys(targetValue);
        for (let i2 = 0; i2 < targetKeys.length; i2++) {
          const targetKey = targetKeys[i2];
          cloned[targetKey] = targetValue[targetKey];
        }
        targetValue = cloned;
      } else {
        targetValue = [];
      }
    }
    const merged = merge3(targetValue, sourceValue, key, target, source, stack);
    if (merged != null) {
      target[key] = merged;
    } else if (Array.isArray(sourceValue)) {
      target[key] = mergeWithDeep(targetValue, sourceValue, merge3, stack);
    } else if (isObjectLike(targetValue) && isObjectLike(sourceValue)) {
      target[key] = mergeWithDeep(targetValue, sourceValue, merge3, stack);
    } else if (targetValue == null && isPlainObject2(sourceValue)) {
      target[key] = mergeWithDeep({}, sourceValue, merge3, stack);
    } else if (targetValue == null && isTypedArray2(sourceValue)) {
      target[key] = cloneDeep2(sourceValue);
    } else if (targetValue === void 0 || sourceValue !== void 0) {
      target[key] = sourceValue;
    }
  }
  return target;
}

// node_modules/es-toolkit/dist/compat/object/merge.mjs
function merge2(object, ...sources) {
  return mergeWith2(object, ...sources, noop);
}

// node_modules/es-toolkit/dist/compat/object/omit.mjs
function omit2(obj, ...keysArr) {
  if (obj == null) {
    return {};
  }
  const result2 = cloneDeep(obj);
  for (let i = 0; i < keysArr.length; i++) {
    let keys2 = keysArr[i];
    switch (typeof keys2) {
      case "object": {
        if (!Array.isArray(keys2)) {
          keys2 = Array.from(keys2);
        }
        for (let j2 = 0; j2 < keys2.length; j2++) {
          const key = keys2[j2];
          unset(result2, key);
        }
        break;
      }
      case "string":
      case "symbol":
      case "number": {
        unset(result2, keys2);
        break;
      }
    }
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/_internal/getSymbolsIn.mjs
function getSymbolsIn(object) {
  const result2 = [];
  while (object) {
    result2.push(...getSymbols(object));
    object = Object.getPrototypeOf(object);
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/object/omitBy.mjs
function omitBy2(obj, shouldOmit) {
  if (obj == null) {
    return {};
  }
  const result2 = {};
  if (shouldOmit == null) {
    return {};
  }
  const keys2 = isArrayLike(obj) ? range(0, obj.length) : [...keysIn(obj), ...getSymbolsIn(obj)];
  for (let i = 0; i < keys2.length; i++) {
    const key = isSymbol(keys2[i]) ? keys2[i] : keys2[i].toString();
    const value = obj[key];
    if (!shouldOmit(value, key, obj)) {
      result2[key] = value;
    }
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/object/pick.mjs
function pick2(obj, ...keysArr) {
  if (isNil2(obj)) {
    return {};
  }
  const result2 = {};
  for (let i = 0; i < keysArr.length; i++) {
    let keys2 = keysArr[i];
    switch (typeof keys2) {
      case "object": {
        if (!Array.isArray(keys2)) {
          if (isArrayLike(keys2)) {
            keys2 = Array.from(keys2);
          } else {
            keys2 = [keys2];
          }
        }
        break;
      }
      case "string":
      case "symbol":
      case "number": {
        keys2 = [keys2];
        break;
      }
    }
    for (const key of keys2) {
      const value = get(obj, key);
      if (value === void 0 && !has(obj, key)) {
        continue;
      }
      if (typeof key === "string" && Object.hasOwn(obj, key)) {
        result2[key] = value;
      } else {
        set(result2, key, value);
      }
    }
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/object/pickBy.mjs
function pickBy2(obj, shouldPick) {
  if (obj == null) {
    return {};
  }
  const result2 = {};
  if (shouldPick == null) {
    return obj;
  }
  const keys2 = isArrayLike(obj) ? range(0, obj.length) : [...keysIn(obj), ...getSymbolsIn(obj)];
  for (let i = 0; i < keys2.length; i++) {
    const key = isSymbol(keys2[i]) ? keys2[i] : keys2[i].toString();
    const value = obj[key];
    if (shouldPick(value, key, obj)) {
      result2[key] = value;
    }
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/object/propertyOf.mjs
function propertyOf(object) {
  return function(path) {
    return get(object, path);
  };
}

// node_modules/es-toolkit/dist/compat/object/result.mjs
function result(object, path, defaultValue) {
  if (isKey(path, object)) {
    path = [path];
  } else if (!Array.isArray(path)) {
    path = toPath(toString(path));
  }
  const pathLength = Math.max(path.length, 1);
  for (let index = 0; index < pathLength; index++) {
    const value = object == null ? void 0 : object[toKey(path[index])];
    if (value === void 0) {
      return typeof defaultValue === "function" ? defaultValue.call(object) : defaultValue;
    }
    object = typeof value === "function" ? value.call(object) : value;
  }
  return object;
}

// node_modules/es-toolkit/dist/compat/object/setWith.mjs
function setWith(obj, path, value, customizer) {
  let customizerFn;
  if (typeof customizer === "function") {
    customizerFn = customizer;
  } else {
    customizerFn = () => void 0;
  }
  return updateWith(obj, path, () => value, customizerFn);
}

// node_modules/es-toolkit/dist/compat/object/toDefaulted.mjs
function toDefaulted(object, ...sources) {
  const cloned = cloneDeep2(object);
  return defaults(cloned, ...sources);
}

// node_modules/es-toolkit/dist/compat/_internal/mapToEntries.mjs
function mapToEntries(map2) {
  const arr = new Array(map2.size);
  const keys2 = map2.keys();
  const values2 = map2.values();
  for (let i = 0; i < arr.length; i++) {
    arr[i] = [keys2.next().value, values2.next().value];
  }
  return arr;
}

// node_modules/es-toolkit/dist/compat/_internal/setToEntries.mjs
function setToEntries(set2) {
  const arr = new Array(set2.size);
  const values2 = set2.values();
  for (let i = 0; i < arr.length; i++) {
    const value = values2.next().value;
    arr[i] = [value, value];
  }
  return arr;
}

// node_modules/es-toolkit/dist/compat/object/toPairs.mjs
function toPairs(object) {
  if (object instanceof Set) {
    return setToEntries(object);
  }
  if (object instanceof Map) {
    return mapToEntries(object);
  }
  const keys$1 = keys(object);
  const result2 = new Array(keys$1.length);
  for (let i = 0; i < keys$1.length; i++) {
    const key = keys$1[i];
    const value = object[key];
    result2[i] = [key, value];
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/object/toPairsIn.mjs
function toPairsIn(object) {
  if (object instanceof Set) {
    return setToEntries(object);
  }
  if (object instanceof Map) {
    return mapToEntries(object);
  }
  const keys2 = keysIn(object);
  const result2 = new Array(keys2.length);
  for (let i = 0; i < keys2.length; i++) {
    const key = keys2[i];
    const value = object[key];
    result2[i] = [key, value];
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/predicate/isBuffer.mjs
function isBuffer2(x3) {
  return isBuffer(x3);
}

// node_modules/es-toolkit/dist/compat/object/transform.mjs
function transform(object, iteratee$1 = identity, accumulator) {
  const isArrayOrBufferOrTypedArray = Array.isArray(object) || isBuffer2(object) || isTypedArray2(object);
  iteratee$1 = iteratee(iteratee$1);
  if (accumulator == null) {
    if (isArrayOrBufferOrTypedArray) {
      accumulator = [];
    } else if (isObject(object) && isFunction(object.constructor)) {
      accumulator = Object.create(Object.getPrototypeOf(object));
    } else {
      accumulator = {};
    }
  }
  if (object == null) {
    return accumulator;
  }
  forEach(object, (value, key, object2) => iteratee$1(accumulator, value, key, object2));
  return accumulator;
}

// node_modules/es-toolkit/dist/compat/object/update.mjs
function update(obj, path, updater) {
  return updateWith(obj, path, updater, () => void 0);
}

// node_modules/es-toolkit/dist/compat/object/valuesIn.mjs
function valuesIn(object) {
  const keys2 = keysIn(object);
  const result2 = new Array(keys2.length);
  for (let i = 0; i < keys2.length; i++) {
    const key = keys2[i];
    result2[i] = object[key];
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/util/bindAll.mjs
function bindAll(object, ...methodNames) {
  if (object == null) {
    return object;
  }
  if (!isObject(object)) {
    return object;
  }
  if (isArray(object) && methodNames.length === 0) {
    return object;
  }
  const methods = [];
  for (let i = 0; i < methodNames.length; i++) {
    const name = methodNames[i];
    if (isArray(name)) {
      methods.push(...name);
    } else if (name && typeof name === "object" && "length" in name) {
      methods.push(...Array.from(name));
    } else {
      methods.push(name);
    }
  }
  if (methods.length === 0) {
    return object;
  }
  for (let i = 0; i < methods.length; i++) {
    const key = methods[i];
    const stringKey = toString(key);
    const func = object[stringKey];
    if (isFunction(func)) {
      object[stringKey] = func.bind(object);
    }
  }
  return object;
}

// node_modules/es-toolkit/dist/compat/predicate/conformsTo.mjs
function conformsTo(target, source) {
  if (source == null) {
    return true;
  }
  if (target == null) {
    return Object.keys(source).length === 0;
  }
  const keys2 = Object.keys(source);
  for (let i = 0; i < keys2.length; i++) {
    const key = keys2[i];
    const predicate = source[key];
    const value = target[key];
    if (value === void 0 && !(key in target) || !predicate(value)) {
      return false;
    }
  }
  return true;
}

// node_modules/es-toolkit/dist/compat/predicate/conforms.mjs
function conforms(source) {
  source = cloneDeep(source);
  return function(object) {
    return conformsTo(object, source);
  };
}

// node_modules/es-toolkit/dist/compat/predicate/isArrayBuffer.mjs
function isArrayBuffer2(value) {
  return isArrayBuffer(value);
}

// node_modules/es-toolkit/dist/compat/predicate/isBoolean.mjs
function isBoolean2(value) {
  return typeof value === "boolean" || value instanceof Boolean;
}

// node_modules/es-toolkit/dist/compat/predicate/isDate.mjs
function isDate2(value) {
  return isDate(value);
}

// node_modules/es-toolkit/dist/compat/predicate/isElement.mjs
function isElement(value) {
  return isObjectLike(value) && value.nodeType === 1 && !isPlainObject2(value);
}

// node_modules/es-toolkit/dist/compat/predicate/isEmpty.mjs
function isEmpty(value) {
  if (value == null) {
    return true;
  }
  if (isArrayLike(value)) {
    if (typeof value.splice !== "function" && typeof value !== "string" && (typeof Buffer === "undefined" || !Buffer.isBuffer(value)) && !isTypedArray2(value) && !isArguments(value)) {
      return false;
    }
    return value.length === 0;
  }
  if (typeof value === "object") {
    if (value instanceof Map || value instanceof Set) {
      return value.size === 0;
    }
    const keys2 = Object.keys(value);
    if (isPrototype(value)) {
      return keys2.filter((x3) => x3 !== "constructor").length === 0;
    }
    return keys2.length === 0;
  }
  return true;
}

// node_modules/es-toolkit/dist/compat/predicate/isEqualWith.mjs
function isEqualWith2(a2, b2, areValuesEqual = noop) {
  if (typeof areValuesEqual !== "function") {
    areValuesEqual = noop;
  }
  return isEqualWith(a2, b2, (...args) => {
    const result2 = areValuesEqual(...args);
    if (result2 !== void 0) {
      return Boolean(result2);
    }
    if (a2 instanceof Map && b2 instanceof Map) {
      return isEqualWith2(Array.from(a2), Array.from(b2), after(2, areValuesEqual));
    }
    if (a2 instanceof Set && b2 instanceof Set) {
      return isEqualWith2(Array.from(a2), Array.from(b2), after(2, areValuesEqual));
    }
  });
}

// node_modules/es-toolkit/dist/compat/predicate/isError.mjs
function isError2(value) {
  return getTag(value) === "[object Error]";
}

// node_modules/es-toolkit/dist/compat/predicate/isFinite.mjs
function isFinite2(value) {
  return Number.isFinite(value);
}

// node_modules/es-toolkit/dist/compat/predicate/isInteger.mjs
function isInteger(value) {
  return Number.isInteger(value);
}

// node_modules/es-toolkit/dist/compat/predicate/isRegExp.mjs
function isRegExp2(value) {
  return isRegExp(value);
}

// node_modules/es-toolkit/dist/compat/predicate/isSafeInteger.mjs
function isSafeInteger(value) {
  return Number.isSafeInteger(value);
}

// node_modules/es-toolkit/dist/compat/predicate/isSet.mjs
function isSet2(value) {
  return isSet(value);
}

// node_modules/es-toolkit/dist/compat/predicate/isWeakMap.mjs
function isWeakMap2(value) {
  return isWeakMap(value);
}

// node_modules/es-toolkit/dist/compat/predicate/isWeakSet.mjs
function isWeakSet2(value) {
  return isWeakSet(value);
}

// node_modules/es-toolkit/dist/compat/_internal/normalizeForCase.mjs
function normalizeForCase(str) {
  if (typeof str !== "string") {
    str = toString(str);
  }
  return str.replace(/['\u2019]/g, "");
}

// node_modules/es-toolkit/dist/compat/string/camelCase.mjs
function camelCase2(str) {
  return camelCase(normalizeForCase(str));
}

// node_modules/es-toolkit/dist/compat/string/deburr.mjs
function deburr2(str) {
  return deburr(toString(str));
}

// node_modules/es-toolkit/dist/compat/string/endsWith.mjs
function endsWith(str, target, position = str.length) {
  return str.endsWith(target, position);
}

// node_modules/es-toolkit/dist/compat/string/escape.mjs
function escape3(string) {
  return escape2(toString(string));
}

// node_modules/es-toolkit/dist/compat/string/escapeRegExp.mjs
function escapeRegExp2(str) {
  return escapeRegExp(toString(str));
}

// node_modules/es-toolkit/dist/compat/string/kebabCase.mjs
function kebabCase2(str) {
  return kebabCase(normalizeForCase(str));
}

// node_modules/es-toolkit/dist/compat/string/lowerCase.mjs
function lowerCase2(str) {
  return lowerCase(normalizeForCase(str));
}

// node_modules/es-toolkit/dist/compat/string/lowerFirst.mjs
function lowerFirst2(str) {
  return lowerFirst(toString(str));
}

// node_modules/es-toolkit/dist/compat/string/pad.mjs
function pad2(str, length, chars = " ") {
  return pad(toString(str), length, chars);
}

// node_modules/es-toolkit/dist/compat/string/padEnd.mjs
function padEnd(str, length = 0, chars = " ") {
  return toString(str).padEnd(length, chars);
}

// node_modules/es-toolkit/dist/compat/string/padStart.mjs
function padStart(str, length = 0, chars = " ") {
  return toString(str).padStart(length, chars);
}

// node_modules/es-toolkit/dist/compat/string/repeat.mjs
function repeat(str, n2, guard) {
  if (guard ? isIterateeCall(str, n2, guard) : n2 === void 0) {
    n2 = 1;
  } else {
    n2 = toInteger(n2);
  }
  return toString(str).repeat(n2);
}

// node_modules/es-toolkit/dist/compat/string/replace.mjs
function replace(target = "", pattern, replacement) {
  if (arguments.length < 3) {
    return toString(target);
  }
  return toString(target).replace(pattern, replacement);
}

// node_modules/es-toolkit/dist/compat/string/snakeCase.mjs
function snakeCase2(str) {
  return snakeCase(normalizeForCase(str));
}

// node_modules/es-toolkit/dist/compat/string/split.mjs
function split(string = "", separator, limit) {
  return toString(string).split(separator, limit);
}

// node_modules/es-toolkit/dist/compat/string/startCase.mjs
function startCase2(str) {
  const words$1 = words(normalizeForCase(str).trim());
  let result2 = "";
  for (let i = 0; i < words$1.length; i++) {
    const word = words$1[i];
    if (result2) {
      result2 += " ";
    }
    if (word === word.toUpperCase()) {
      result2 += word;
    } else {
      result2 += word[0].toUpperCase() + word.slice(1).toLowerCase();
    }
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/string/startsWith.mjs
function startsWith(str, target, position = 0) {
  return str.startsWith(target, position);
}

// node_modules/es-toolkit/dist/compat/string/template.mjs
var esTemplateRegExp = /\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g;
var unEscapedRegExp = /['\n\r\u2028\u2029\\]/g;
var noMatchExp = /($^)/;
var escapeMap = /* @__PURE__ */ new Map([
  ["\\", "\\"],
  ["'", "'"],
  ["\n", "n"],
  ["\r", "r"],
  ["\u2028", "u2028"],
  ["\u2029", "u2029"]
]);
function escapeString(match) {
  return `\\${escapeMap.get(match)}`;
}
var templateSettings = {
  escape: /<%-([\s\S]+?)%>/g,
  evaluate: /<%([\s\S]+?)%>/g,
  interpolate: /<%=([\s\S]+?)%>/g,
  variable: "",
  imports: {
    _: {
      escape: escape3,
      template
    }
  }
};
function template(string, options, guard) {
  var _a, _b, _c;
  string = toString(string);
  if (guard) {
    options = templateSettings;
  }
  options = defaults({ ...options }, templateSettings);
  const delimitersRegExp = new RegExp([
    ((_a = options.escape) == null ? void 0 : _a.source) ?? noMatchExp.source,
    ((_b = options.interpolate) == null ? void 0 : _b.source) ?? noMatchExp.source,
    options.interpolate ? esTemplateRegExp.source : noMatchExp.source,
    ((_c = options.evaluate) == null ? void 0 : _c.source) ?? noMatchExp.source,
    "$"
  ].join("|"), "g");
  let lastIndex = 0;
  let isEvaluated = false;
  let source = `__p += ''`;
  for (const match of string.matchAll(delimitersRegExp)) {
    const [fullMatch, escapeValue, interpolateValue, esTemplateValue, evaluateValue] = match;
    const { index } = match;
    source += ` + '${string.slice(lastIndex, index).replace(unEscapedRegExp, escapeString)}'`;
    if (escapeValue) {
      source += ` + _.escape(${escapeValue})`;
    }
    if (interpolateValue) {
      source += ` + ((${interpolateValue}) == null ? '' : ${interpolateValue})`;
    } else if (esTemplateValue) {
      source += ` + ((${esTemplateValue}) == null ? '' : ${esTemplateValue})`;
    }
    if (evaluateValue) {
      source += `;
${evaluateValue};
 __p += ''`;
      isEvaluated = true;
    }
    lastIndex = index + fullMatch.length;
  }
  const imports = defaults({ ...options.imports }, templateSettings.imports);
  const importsKeys = Object.keys(imports);
  const importValues = Object.values(imports);
  const sourceURL = `//# sourceURL=${options.sourceURL ? String(options.sourceURL).replace(/[\r\n]/g, " ") : `es-toolkit.templateSource[${Date.now()}]`}
`;
  const compiledFunction = `function(${options.variable || "obj"}) {
    let __p = '';
    ${options.variable ? "" : "if (obj == null) { obj = {}; }"}
    ${isEvaluated ? `function print() { __p += Array.prototype.join.call(arguments, ''); }` : ""}
    ${options.variable ? source : `with(obj) {
${source}
}`}
    return __p;
  }`;
  const result2 = attempt2(() => new Function(...importsKeys, `${sourceURL}return ${compiledFunction}`)(...importValues));
  result2.source = compiledFunction;
  if (result2 instanceof Error) {
    throw result2;
  }
  return result2;
}

// node_modules/es-toolkit/dist/compat/string/toLower.mjs
function toLower(value) {
  return toString(value).toLowerCase();
}

// node_modules/es-toolkit/dist/compat/string/toUpper.mjs
function toUpper(value) {
  return toString(value).toUpperCase();
}

// node_modules/es-toolkit/dist/compat/string/trim.mjs
function trim2(str, chars, guard) {
  if (str == null) {
    return "";
  }
  if (guard != null || chars == null) {
    return str.toString().trim();
  }
  switch (typeof chars) {
    case "string": {
      return trim(str, chars.toString().split(""));
    }
    case "object": {
      if (Array.isArray(chars)) {
        return trim(str, chars.flatMap((x3) => x3.toString().split("")));
      } else {
        return trim(str, chars.toString().split(""));
      }
    }
  }
}

// node_modules/es-toolkit/dist/compat/string/trimEnd.mjs
function trimEnd2(str, chars, guard) {
  if (str == null) {
    return "";
  }
  if (guard != null || chars == null) {
    return str.toString().trimEnd();
  }
  switch (typeof chars) {
    case "string": {
      return trimEnd(str, chars.toString().split(""));
    }
    case "object": {
      if (Array.isArray(chars)) {
        return trimEnd(str, chars.flatMap((x3) => x3.toString().split("")));
      } else {
        return trimEnd(str, chars.toString().split(""));
      }
    }
  }
}

// node_modules/es-toolkit/dist/compat/string/trimStart.mjs
function trimStart2(str, chars, guard) {
  if (str == null) {
    return "";
  }
  if (guard != null || chars == null) {
    return str.toString().trimStart();
  }
  switch (typeof chars) {
    case "string": {
      return trimStart(str, chars.toString().split(""));
    }
    case "object": {
      if (Array.isArray(chars)) {
        return trimStart(str, chars.flatMap((x3) => x3.toString().split("")));
      } else {
        return trimStart(str, chars.toString().split(""));
      }
    }
  }
}

// node_modules/es-toolkit/dist/compat/string/unescape.mjs
function unescape3(str) {
  return unescape2(toString(str));
}

// node_modules/es-toolkit/dist/compat/string/upperCase.mjs
function upperCase2(str) {
  return upperCase(normalizeForCase(str));
}

// node_modules/es-toolkit/dist/compat/string/upperFirst.mjs
function upperFirst2(str) {
  return upperFirst(toString(str));
}

// node_modules/es-toolkit/dist/compat/string/words.mjs
var rNonCharLatin = "\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf\\xd7\\xf7";
var rUnicodeUpper = "\\p{Lu}";
var rUnicodeLower = "\\p{Ll}";
var rMisc = "(?:[\\p{Lm}\\p{Lo}]\\p{M}*)";
var rNumber = "\\d";
var rUnicodeOptContrLower = "(?:['’](?:d|ll|m|re|s|t|ve))?";
var rUnicodeOptContrUpper = "(?:['’](?:D|LL|M|RE|S|T|VE))?";
var rUnicodeBreak = `[\\p{Z}\\p{P}${rNonCharLatin}]`;
var rUnicodeMiscUpper = `(?:${rUnicodeUpper}|${rMisc})`;
var rUnicodeMiscLower = `(?:${rUnicodeLower}|${rMisc})`;
var rUnicodeWord = RegExp([
  `${rUnicodeUpper}?${rUnicodeLower}+${rUnicodeOptContrLower}(?=${rUnicodeBreak}|${rUnicodeUpper}|$)`,
  `${rUnicodeMiscUpper}+${rUnicodeOptContrUpper}(?=${rUnicodeBreak}|${rUnicodeUpper}${rUnicodeMiscLower}|$)`,
  `${rUnicodeUpper}?${rUnicodeMiscLower}+${rUnicodeOptContrLower}`,
  `${rUnicodeUpper}+${rUnicodeOptContrUpper}`,
  `${rNumber}*(?:1ST|2ND|3RD|(?![123])${rNumber}TH)(?=\\b|[a-z_])`,
  `${rNumber}*(?:1st|2nd|3rd|(?![123])${rNumber}th)(?=\\b|[A-Z_])`,
  `${rNumber}+`,
  "\\p{Emoji_Presentation}",
  "\\p{Extended_Pictographic}"
].join("|"), "gu");
function words2(str, pattern = rUnicodeWord, guard) {
  const input = toString(str);
  pattern = guard ? rUnicodeWord : pattern;
  const words3 = Array.from(input.match(pattern) ?? []);
  return words3.filter((x3) => x3 !== "");
}

// node_modules/es-toolkit/dist/compat/util/cond.mjs
function cond(pairs) {
  const length = pairs.length;
  const processedPairs = pairs.map((pair) => {
    const predicate = pair[0];
    const func = pair[1];
    if (!isFunction(func)) {
      throw new TypeError("Expected a function");
    }
    return [iteratee(predicate), func];
  });
  return function(...args) {
    for (let i = 0; i < length; i++) {
      const pair = processedPairs[i];
      const predicate = pair[0];
      const func = pair[1];
      if (predicate.apply(this, args)) {
        return func.apply(this, args);
      }
    }
  };
}

// node_modules/es-toolkit/dist/compat/util/constant.mjs
function constant(value) {
  return () => value;
}

// node_modules/es-toolkit/dist/compat/util/defaultTo.mjs
function defaultTo(value, defaultValue) {
  if (value == null || Number.isNaN(value)) {
    return defaultValue;
  }
  return value;
}

// node_modules/es-toolkit/dist/compat/util/gt.mjs
function gt(value, other) {
  if (typeof value === "string" && typeof other === "string") {
    return value > other;
  }
  return toNumber(value) > toNumber(other);
}

// node_modules/es-toolkit/dist/compat/util/gte.mjs
function gte(value, other) {
  if (typeof value === "string" && typeof other === "string") {
    return value >= other;
  }
  return toNumber(value) >= toNumber(other);
}

// node_modules/es-toolkit/dist/compat/util/invoke.mjs
function invoke(object, path, args = []) {
  if (object == null) {
    return;
  }
  switch (typeof path) {
    case "string": {
      if (typeof object === "object" && Object.hasOwn(object, path)) {
        return invokeImpl(object, [path], args);
      }
      return invokeImpl(object, toPath(path), args);
    }
    case "number":
    case "symbol": {
      return invokeImpl(object, [path], args);
    }
    default: {
      if (Array.isArray(path)) {
        return invokeImpl(object, path, args);
      } else {
        return invokeImpl(object, [path], args);
      }
    }
  }
}
function invokeImpl(object, path, args) {
  const parent = get(object, path.slice(0, -1), object);
  if (parent == null) {
    return void 0;
  }
  let lastKey = last2(path);
  const lastValue = lastKey == null ? void 0 : lastKey.valueOf();
  if (typeof lastValue === "number") {
    lastKey = toKey(lastValue);
  } else {
    lastKey = String(lastKey);
  }
  const func = get(parent, lastKey);
  return func == null ? void 0 : func.apply(parent, args);
}

// node_modules/es-toolkit/dist/compat/util/lt.mjs
function lt2(value, other) {
  if (typeof value === "string" && typeof other === "string") {
    return value < other;
  }
  return toNumber(value) < toNumber(other);
}

// node_modules/es-toolkit/dist/compat/util/lte.mjs
function lte(value, other) {
  if (typeof value === "string" && typeof other === "string") {
    return value <= other;
  }
  return toNumber(value) <= toNumber(other);
}

// node_modules/es-toolkit/dist/compat/util/method.mjs
function method(path, ...args) {
  return function(object) {
    return invoke(object, path, args);
  };
}

// node_modules/es-toolkit/dist/compat/util/methodOf.mjs
function methodOf(object, ...args) {
  return function(path) {
    return invoke(object, path, args);
  };
}

// node_modules/es-toolkit/dist/compat/util/now.mjs
function now() {
  return Date.now();
}

// node_modules/es-toolkit/dist/compat/util/over.mjs
function over(...iteratees) {
  if (iteratees.length === 1 && Array.isArray(iteratees[0])) {
    iteratees = iteratees[0];
  }
  const funcs = iteratees.map((item) => iteratee(item));
  return function(...args) {
    return funcs.map((func) => func.apply(this, args));
  };
}

// node_modules/es-toolkit/dist/compat/util/overEvery.mjs
function overEvery(...predicates) {
  return function(...values2) {
    for (let i = 0; i < predicates.length; ++i) {
      const predicate = predicates[i];
      if (!Array.isArray(predicate)) {
        if (!iteratee(predicate).apply(this, values2)) {
          return false;
        }
        continue;
      }
      for (let j2 = 0; j2 < predicate.length; ++j2) {
        if (!iteratee(predicate[j2]).apply(this, values2)) {
          return false;
        }
      }
    }
    return true;
  };
}

// node_modules/es-toolkit/dist/compat/util/overSome.mjs
function overSome(...predicates) {
  return function(...values2) {
    for (let i = 0; i < predicates.length; ++i) {
      const predicate = predicates[i];
      if (!Array.isArray(predicate)) {
        if (iteratee(predicate).apply(this, values2)) {
          return true;
        }
        continue;
      }
      for (let j2 = 0; j2 < predicate.length; ++j2) {
        if (iteratee(predicate[j2]).apply(this, values2)) {
          return true;
        }
      }
    }
    return false;
  };
}

// node_modules/es-toolkit/dist/compat/util/stubArray.mjs
function stubArray() {
  return [];
}

// node_modules/es-toolkit/dist/compat/util/stubFalse.mjs
function stubFalse() {
  return false;
}

// node_modules/es-toolkit/dist/compat/util/stubObject.mjs
function stubObject() {
  return {};
}

// node_modules/es-toolkit/dist/compat/util/stubString.mjs
function stubString() {
  return "";
}

// node_modules/es-toolkit/dist/compat/util/stubTrue.mjs
function stubTrue() {
  return true;
}

// node_modules/es-toolkit/dist/compat/_internal/MAX_ARRAY_LENGTH.mjs
var MAX_ARRAY_LENGTH4 = 4294967295;

// node_modules/es-toolkit/dist/compat/util/toLength.mjs
function toLength(value) {
  if (value == null) {
    return 0;
  }
  const length = Math.floor(Number(value));
  return clamp2(length, 0, MAX_ARRAY_LENGTH4);
}

// node_modules/es-toolkit/dist/compat/util/toPlainObject.mjs
function toPlainObject(value) {
  const plainObject = {};
  const valueKeys = keysIn(value);
  for (let i = 0; i < valueKeys.length; i++) {
    const key = valueKeys[i];
    const objValue = value[key];
    if (key === "__proto__") {
      Object.defineProperty(plainObject, key, {
        configurable: true,
        enumerable: true,
        value: objValue,
        writable: true
      });
    } else {
      plainObject[key] = objValue;
    }
  }
  return plainObject;
}

// node_modules/es-toolkit/dist/compat/_internal/MAX_SAFE_INTEGER.mjs
var MAX_SAFE_INTEGER = Number.MAX_SAFE_INTEGER;

// node_modules/es-toolkit/dist/compat/util/toSafeInteger.mjs
function toSafeInteger(value) {
  if (value == null) {
    return 0;
  }
  return clamp2(toInteger(value), -MAX_SAFE_INTEGER, MAX_SAFE_INTEGER);
}

// node_modules/es-toolkit/dist/compat/util/uniqueId.mjs
var idCounter = 0;
function uniqueId(prefix = "") {
  const id = ++idCounter;
  return `${prefix}${id}`;
}

// node_modules/es-toolkit/dist/compat/compat.mjs
var compat_exports = {};
__export(compat_exports, {
  add: () => add,
  after: () => after2,
  ary: () => ary2,
  assign: () => assign,
  assignIn: () => assignIn,
  assignInWith: () => assignInWith,
  assignWith: () => assignWith,
  at: () => at3,
  attempt: () => attempt2,
  before: () => before2,
  bind: () => bind,
  bindAll: () => bindAll,
  bindKey: () => bindKey,
  camelCase: () => camelCase2,
  capitalize: () => capitalize,
  castArray: () => castArray,
  ceil: () => ceil,
  chunk: () => chunk2,
  clamp: () => clamp2,
  clone: () => clone2,
  cloneDeep: () => cloneDeep2,
  cloneDeepWith: () => cloneDeepWith2,
  cloneWith: () => cloneWith,
  compact: () => compact2,
  concat: () => concat,
  cond: () => cond,
  conforms: () => conforms,
  conformsTo: () => conformsTo,
  constant: () => constant,
  create: () => create,
  curry: () => curry2,
  curryRight: () => curryRight2,
  debounce: () => debounce2,
  deburr: () => deburr2,
  defaultTo: () => defaultTo,
  defaults: () => defaults,
  defer: () => defer,
  delay: () => delay2,
  difference: () => difference2,
  differenceBy: () => differenceBy2,
  differenceWith: () => differenceWith2,
  divide: () => divide,
  drop: () => drop2,
  dropRight: () => dropRight2,
  dropRightWhile: () => dropRightWhile2,
  dropWhile: () => dropWhile2,
  each: () => forEach,
  eachRight: () => forEachRight2,
  endsWith: () => endsWith,
  eq: () => eq,
  escape: () => escape3,
  escapeRegExp: () => escapeRegExp2,
  every: () => every,
  extend: () => assignIn,
  extendWith: () => assignInWith,
  fill: () => fill2,
  filter: () => filter,
  find: () => find,
  findIndex: () => findIndex,
  findKey: () => findKey2,
  findLast: () => findLast,
  findLastIndex: () => findLastIndex,
  first: () => head2,
  flatMap: () => flatMap2,
  flatten: () => flatten2,
  flattenDeep: () => flattenDeep2,
  flattenDepth: () => flattenDepth,
  flip: () => flip,
  floor: () => floor,
  flow: () => flow2,
  flowRight: () => flowRight2,
  forEach: () => forEach,
  forEachRight: () => forEachRight2,
  forIn: () => forIn,
  forInRight: () => forInRight,
  forOwn: () => forOwn,
  forOwnRight: () => forOwnRight,
  fromPairs: () => fromPairs,
  functions: () => functions,
  functionsIn: () => functionsIn,
  get: () => get,
  groupBy: () => groupBy2,
  gt: () => gt,
  gte: () => gte,
  has: () => has,
  hasIn: () => hasIn,
  head: () => head2,
  identity: () => identity,
  inRange: () => inRange2,
  includes: () => includes,
  indexOf: () => indexOf,
  initial: () => initial2,
  intersection: () => intersection2,
  intersectionBy: () => intersectionBy2,
  intersectionWith: () => intersectionWith2,
  invert: () => invert,
  invertBy: () => invertBy,
  invoke: () => invoke,
  invokeMap: () => invokeMap,
  isArguments: () => isArguments,
  isArray: () => isArray,
  isArrayBuffer: () => isArrayBuffer2,
  isArrayLike: () => isArrayLike,
  isArrayLikeObject: () => isArrayLikeObject,
  isBoolean: () => isBoolean2,
  isBuffer: () => isBuffer2,
  isDate: () => isDate2,
  isElement: () => isElement,
  isEmpty: () => isEmpty,
  isEqual: () => isEqual,
  isEqualWith: () => isEqualWith2,
  isError: () => isError2,
  isFinite: () => isFinite2,
  isFunction: () => isFunction,
  isInteger: () => isInteger,
  isLength: () => isLength,
  isMap: () => isMap2,
  isMatch: () => isMatch,
  isNaN: () => isNaN2,
  isNative: () => isNative,
  isNil: () => isNil2,
  isNull: () => isNull,
  isNumber: () => isNumber,
  isObject: () => isObject,
  isObjectLike: () => isObjectLike,
  isPlainObject: () => isPlainObject2,
  isRegExp: () => isRegExp2,
  isSafeInteger: () => isSafeInteger,
  isSet: () => isSet2,
  isString: () => isString2,
  isSymbol: () => isSymbol,
  isTypedArray: () => isTypedArray2,
  isUndefined: () => isUndefined,
  isWeakMap: () => isWeakMap2,
  isWeakSet: () => isWeakSet2,
  iteratee: () => iteratee,
  join: () => join,
  kebabCase: () => kebabCase2,
  keyBy: () => keyBy2,
  keys: () => keys,
  keysIn: () => keysIn,
  last: () => last2,
  lastIndexOf: () => lastIndexOf,
  lowerCase: () => lowerCase2,
  lowerFirst: () => lowerFirst2,
  lt: () => lt2,
  lte: () => lte,
  map: () => map,
  mapKeys: () => mapKeys2,
  mapValues: () => mapValues2,
  matches: () => matches,
  matchesProperty: () => matchesProperty,
  max: () => max,
  maxBy: () => maxBy2,
  mean: () => mean2,
  meanBy: () => meanBy2,
  memoize: () => memoize2,
  merge: () => merge2,
  mergeWith: () => mergeWith2,
  method: () => method,
  methodOf: () => methodOf,
  min: () => min,
  minBy: () => minBy2,
  multiply: () => multiply,
  negate: () => negate2,
  noop: () => noop,
  now: () => now,
  nth: () => nth,
  nthArg: () => nthArg,
  omit: () => omit2,
  omitBy: () => omitBy2,
  once: () => once,
  orderBy: () => orderBy2,
  over: () => over,
  overEvery: () => overEvery,
  overSome: () => overSome,
  pad: () => pad2,
  padEnd: () => padEnd,
  padStart: () => padStart,
  parseInt: () => parseInt2,
  partial: () => partial2,
  partialRight: () => partialRight2,
  partition: () => partition2,
  pick: () => pick2,
  pickBy: () => pickBy2,
  property: () => property,
  propertyOf: () => propertyOf,
  pull: () => pull2,
  pullAll: () => pullAll,
  pullAllBy: () => pullAllBy,
  pullAllWith: () => pullAllWith,
  pullAt: () => pullAt2,
  random: () => random2,
  range: () => range2,
  rangeRight: () => rangeRight2,
  rearg: () => rearg,
  reduce: () => reduce,
  reduceRight: () => reduceRight,
  reject: () => reject,
  remove: () => remove2,
  repeat: () => repeat,
  replace: () => replace,
  rest: () => rest2,
  result: () => result,
  reverse: () => reverse,
  round: () => round2,
  sample: () => sample2,
  sampleSize: () => sampleSize2,
  set: () => set,
  setWith: () => setWith,
  shuffle: () => shuffle2,
  size: () => size,
  slice: () => slice,
  snakeCase: () => snakeCase2,
  some: () => some,
  sortBy: () => sortBy2,
  sortedIndex: () => sortedIndex,
  sortedIndexBy: () => sortedIndexBy,
  sortedIndexOf: () => sortedIndexOf,
  sortedLastIndex: () => sortedLastIndex,
  sortedLastIndexBy: () => sortedLastIndexBy,
  sortedLastIndexOf: () => sortedLastIndexOf,
  split: () => split,
  spread: () => spread2,
  startCase: () => startCase2,
  startsWith: () => startsWith,
  stubArray: () => stubArray,
  stubFalse: () => stubFalse,
  stubObject: () => stubObject,
  stubString: () => stubString,
  stubTrue: () => stubTrue,
  subtract: () => subtract,
  sum: () => sum2,
  sumBy: () => sumBy2,
  tail: () => tail2,
  take: () => take2,
  takeRight: () => takeRight2,
  takeRightWhile: () => takeRightWhile2,
  takeWhile: () => takeWhile2,
  template: () => template,
  templateSettings: () => templateSettings,
  throttle: () => throttle2,
  times: () => times,
  toArray: () => toArray2,
  toDefaulted: () => toDefaulted,
  toFinite: () => toFinite,
  toInteger: () => toInteger,
  toLength: () => toLength,
  toLower: () => toLower,
  toNumber: () => toNumber,
  toPairs: () => toPairs,
  toPairsIn: () => toPairsIn,
  toPath: () => toPath,
  toPlainObject: () => toPlainObject,
  toSafeInteger: () => toSafeInteger,
  toString: () => toString,
  toUpper: () => toUpper,
  transform: () => transform,
  trim: () => trim2,
  trimEnd: () => trimEnd2,
  trimStart: () => trimStart2,
  unary: () => unary,
  unescape: () => unescape3,
  union: () => union2,
  unionBy: () => unionBy2,
  unionWith: () => unionWith2,
  uniq: () => uniq2,
  uniqBy: () => uniqBy2,
  uniqWith: () => uniqWith2,
  uniqueId: () => uniqueId,
  unset: () => unset,
  unzip: () => unzip2,
  unzipWith: () => unzipWith2,
  update: () => update,
  updateWith: () => updateWith,
  upperCase: () => upperCase2,
  upperFirst: () => upperFirst2,
  values: () => values,
  valuesIn: () => valuesIn,
  without: () => without2,
  words: () => words2,
  wrap: () => wrap,
  xor: () => xor2,
  xorBy: () => xorBy2,
  xorWith: () => xorWith2,
  zip: () => zip2,
  zipObject: () => zipObject2,
  zipObjectDeep: () => zipObjectDeep,
  zipWith: () => zipWith2
});

// node_modules/es-toolkit/dist/compat/toolkit.mjs
var toolkit = (value) => {
  return value;
};
Object.assign(toolkit, compat_exports);
toolkit.partial.placeholder = toolkit;
toolkit.partialRight.placeholder = toolkit;

// node_modules/@inertiajs/react/dist/index.esm.js
var import_react9 = __toESM(require_react());
var import_react10 = __toESM(require_react());
var import_react11 = __toESM(require_react());
var import_react12 = __toESM(require_react());
var import_react13 = __toESM(require_react());
var ie2 = (0, import_react3.createContext)(void 0);
ie2.displayName = "InertiaHeadContext";
var K2 = ie2;
var pe2 = (0, import_react4.createContext)(void 0);
pe2.displayName = "InertiaPageContext";
var B2 = pe2;
function _2({ children: s, initialPage: t, initialComponent: a2, resolveComponent: p, titleCallback: i, onHeadUpdate: g2 }) {
  let [u, f2] = (0, import_react2.useState)({ component: a2 || null, page: t, key: null }), c = (0, import_react2.useMemo)(() => Ie(typeof window > "u", i || ((l) => l), g2 || (() => {
  })), []);
  if ((0, import_react2.useEffect)(() => {
    Wr.init({ initialPage: t, resolveComponent: p, swapComponent: async ({ component: l, page: e, preserveState: d }) => {
      f2((r4) => ({ component: l, page: e, key: d ? r4.key : Date.now() }));
    } }), Wr.on("navigate", () => c.forceUpdate());
  }, []), !u.component) return (0, import_react2.createElement)(K2.Provider, { value: c }, (0, import_react2.createElement)(B2.Provider, { value: u.page }, null));
  let m2 = s || (({ Component: l, props: e, key: d }) => {
    let r4 = (0, import_react2.createElement)(l, { key: d, ...e });
    return typeof l.layout == "function" ? l.layout(r4) : Array.isArray(l.layout) ? l.layout.concat(r4).reverse().reduce((h, F2) => (0, import_react2.createElement)(F2, { children: h, ...e })) : r4;
  });
  return (0, import_react2.createElement)(K2.Provider, { value: c }, (0, import_react2.createElement)(B2.Provider, { value: u.page }, m2({ Component: u.component, key: u.key, props: u.page.props })));
}
_2.displayName = "Inertia";
async function ce2({ id: s = "app", resolve: t, setup: a2, title: p, progress: i = {}, page: g2, render: u }) {
  let f2 = typeof window > "u", c = f2 ? null : document.getElementById(s), m2 = g2 || JSON.parse(c.dataset.page), l = (r4) => Promise.resolve(t(r4)).then((h) => h.default || h), e = [], d = await Promise.all([l(m2.component), Wr.decryptHistory().catch(() => {
  })]).then(([r4]) => a2({ el: c, App: _2, props: { initialPage: m2, initialComponent: r4, resolveComponent: l, titleCallback: p, onHeadUpdate: f2 ? (h) => e = h : null } }));
  if (!f2 && i && Me(i), f2) {
    let r4 = await u((0, import_react.createElement)("div", { id: s, "data-page": JSON.stringify(m2) }, d));
    return { head: e, body: r4 };
  }
}
function q2() {
  let s = (0, import_react6.useContext)(B2);
  if (!s) throw new Error("usePage must be used within the Inertia component");
  return s;
}
var de2 = (s) => (s = new URL(s.href), s.hash = "", s);
var Ke2 = (s, t) => de2(s).href === de2(t).href;
var fe2 = ({ children: s, data: t, fallback: a2 }) => {
  if (!t) throw new Error("`<Deferred>` requires a `data` prop to be a string or array of strings");
  let [p, i] = (0, import_react5.useState)(false), g2 = q2().props, u = (0, import_react5.useMemo)(() => Array.isArray(t) ? t : [t], [t]);
  return (0, import_react5.useEffect)(() => {
    let f2 = me2.on("start", (c) => {
      let m2 = c.detail.visit.only.length > 0 || c.detail.visit.except.length > 0, l = c.detail.visit.only.find((e) => u.includes(e));
      Ke2(c.detail.visit.url, window.location) && (!m2 || l) && i(false);
    });
    return () => {
      f2();
    };
  }, []), (0, import_react5.useEffect)(() => {
    i(u.every((f2) => g2[f2] !== void 0));
  }, [g2, u]), p ? s : a2;
};
fe2.displayName = "InertiaDeferred";
var Be2 = fe2;
var je2 = function({ children: s, title: t }) {
  let a2 = (0, import_react7.useContext)(K2), p = (0, import_react7.useMemo)(() => a2.createProvider(), [a2]);
  (0, import_react7.useEffect)(() => () => {
    p.disconnect();
  }, [p]);
  function i(e) {
    return ["area", "base", "br", "col", "embed", "hr", "img", "input", "keygen", "link", "meta", "param", "source", "track", "wbr"].indexOf(e.type) > -1;
  }
  function g2(e) {
    let d = Object.keys(e.props).reduce((r4, h) => {
      if (["head-key", "children", "dangerouslySetInnerHTML"].includes(h)) return r4;
      let F2 = e.props[h];
      return F2 === "" ? r4 + ` ${h}` : r4 + ` ${h}="${F2}"`;
    }, "");
    return `<${e.type}${d}>`;
  }
  function u(e) {
    return typeof e.props.children == "string" ? e.props.children : e.props.children.reduce((d, r4) => d + f2(r4), "");
  }
  function f2(e) {
    let d = g2(e);
    return e.props.children && (d += u(e)), e.props.dangerouslySetInnerHTML && (d += e.props.dangerouslySetInnerHTML.__html), i(e) || (d += `</${e.type}>`), d;
  }
  function c(e) {
    return import_react7.default.cloneElement(e, { inertia: e.props["head-key"] !== void 0 ? e.props["head-key"] : "" });
  }
  function m2(e) {
    return f2(c(e));
  }
  function l(e) {
    let d = import_react7.default.Children.toArray(e).filter((r4) => r4).map((r4) => m2(r4));
    return t && !d.find((r4) => r4.startsWith("<title")) && d.push(`<title inertia>${t}</title>`), d;
  }
  return p.update(l(s)), null;
};
var $e2 = je2;
var x2 = () => {
};
var he2 = (0, import_react8.forwardRef)(({ children: s, as: t = "a", data: a2 = {}, href: p, method: i = "get", preserveScroll: g2 = false, preserveState: u = null, replace: f2 = false, only: c = [], except: m2 = [], headers: l = {}, queryStringArrayFormat: e = "brackets", async: d = false, onClick: r4 = x2, onCancelToken: h = x2, onBefore: F2 = x2, onStart: I = x2, onProgress: E2 = x2, onFinish: Y2 = x2, onCancel: N2 = x2, onSuccess: z2 = x2, onError: M = x2, prefetch: v2 = false, cacheFor: b2 = 0, ...G2 }, X2) => {
  let [Z2, j2] = (0, import_react8.useState)(0), L2 = (0, import_react8.useRef)(null);
  t = t.toLowerCase(), i = typeof p == "object" ? p.method : i.toLowerCase();
  let [k2, ee2] = qe(i, typeof p == "object" ? p.url : p || "", a2, e), A2 = k2;
  a2 = ee2;
  let $2 = { data: a2, method: i, preserveScroll: g2, preserveState: u ?? i !== "get", replace: f2, only: c, except: m2, headers: l, async: d }, W2 = { ...$2, onCancelToken: h, onBefore: F2, onStart(o) {
    j2((w) => w + 1), I(o);
  }, onProgress: E2, onFinish(o) {
    j2((w) => w - 1), Y2(o);
  }, onCancel: N2, onSuccess: z2, onError: M }, H2 = () => {
    Wr.prefetch(A2, $2, { cacheFor: te2 });
  }, R = (0, import_react8.useMemo)(() => v2 === true ? ["hover"] : v2 === false ? [] : Array.isArray(v2) ? v2 : [v2], Array.isArray(v2) ? v2 : [v2]), te2 = (0, import_react8.useMemo)(() => b2 !== 0 ? b2 : R.length === 1 && R[0] === "click" ? 0 : 3e4, [b2, R]);
  (0, import_react8.useEffect)(() => () => {
    clearTimeout(L2.current);
  }, []), (0, import_react8.useEffect)(() => {
    R.includes("mount") && setTimeout(() => H2());
  }, R);
  let n2 = { onClick: (o) => {
    r4(o), Ne(o) && (o.preventDefault(), Wr.visit(A2, W2));
  } }, y2 = { onMouseEnter: () => {
    L2.current = window.setTimeout(() => {
      H2();
    }, 75);
  }, onMouseLeave: () => {
    clearTimeout(L2.current);
  }, onClick: n2.onClick }, P2 = { onMouseDown: (o) => {
    Ne(o) && (o.preventDefault(), H2());
  }, onMouseUp: (o) => {
    o.preventDefault(), Wr.visit(A2, W2);
  }, onClick: (o) => {
    r4(o), Ne(o) && o.preventDefault();
  } };
  return i !== "get" && (t = "button"), (0, import_react8.createElement)(t, { ...G2, ...{ a: { href: A2 }, button: { type: "button" } }[t] || {}, ref: X2, ...R.includes("hover") ? y2 : R.includes("click") ? P2 : n2, "data-loading": Z2 > 0 ? "" : void 0 }, s);
});
he2.displayName = "InertiaLink";
var Ye2 = he2;
function V2(s, t) {
  let [a2, p] = (0, import_react10.useState)(() => {
    let i = Wr.restore(t);
    return i !== void 0 ? i : s;
  });
  return (0, import_react10.useEffect)(() => {
    Wr.remember(a2, t);
  }, [a2, t]), [a2, p];
}
function ve2(s, t) {
  let a2 = (0, import_react9.useRef)(null), p = typeof s == "string" ? s : null, [i, g2] = (0, import_react9.useState)((typeof s == "string" ? t : s) || {}), u = (0, import_react9.useRef)(null), f2 = (0, import_react9.useRef)(null), [c, m2] = p ? V2(i, `${p}:data`) : (0, import_react9.useState)(i), [l, e] = p ? V2({}, `${p}:errors`) : (0, import_react9.useState)({}), [d, r4] = (0, import_react9.useState)(false), [h, F2] = (0, import_react9.useState)(false), [I, E2] = (0, import_react9.useState)(null), [Y2, N2] = (0, import_react9.useState)(false), [z2, M] = (0, import_react9.useState)(false), v2 = (0, import_react9.useRef)((n2) => n2);
  (0, import_react9.useEffect)(() => (a2.current = true, () => {
    a2.current = false;
  }), []);
  let b2 = (0, import_react9.useCallback)((...n2) => {
    let y2 = typeof n2[0] == "object", P2 = y2 ? n2[0].method : n2[0], C2 = y2 ? n2[0].url : n2[1], o = (y2 ? n2[1] : n2[2]) ?? {}, w = { ...o, onCancelToken: (S2) => {
      if (u.current = S2, o.onCancelToken) return o.onCancelToken(S2);
    }, onBefore: (S2) => {
      if (N2(false), M(false), clearTimeout(f2.current), o.onBefore) return o.onBefore(S2);
    }, onStart: (S2) => {
      if (F2(true), o.onStart) return o.onStart(S2);
    }, onProgress: (S2) => {
      if (E2(S2), o.onProgress) return o.onProgress(S2);
    }, onSuccess: (S2) => {
      if (a2.current && (F2(false), E2(null), e({}), r4(false), N2(true), M(true), g2(cloneDeep(c)), f2.current = setTimeout(() => {
        a2.current && M(false);
      }, 2e3)), o.onSuccess) return o.onSuccess(S2);
    }, onError: (S2) => {
      if (a2.current && (F2(false), E2(null), e(S2), r4(true)), o.onError) return o.onError(S2);
    }, onCancel: () => {
      if (a2.current && (F2(false), E2(null)), o.onCancel) return o.onCancel();
    }, onFinish: (S2) => {
      if (a2.current && (F2(false), E2(null)), u.current = null, o.onFinish) return o.onFinish(S2);
    } };
    P2 === "delete" ? Wr.delete(C2, { ...w, data: v2.current(c) }) : Wr[P2](C2, v2.current(c), w);
  }, [c, e, v2]), G2 = (0, import_react9.useCallback)((n2, y2) => {
    m2(typeof n2 == "string" ? (P2) => set(cloneDeep(P2), n2, y2) : typeof n2 == "function" ? (P2) => n2(P2) : n2);
  }, [m2]), X2 = (0, import_react9.useCallback)((n2, y2) => {
    g2(typeof n2 > "u" ? () => c : (P2) => typeof n2 == "string" ? set(cloneDeep(P2), n2, y2) : Object.assign(cloneDeep(P2), n2));
  }, [c, g2]), Z2 = (0, import_react9.useCallback)((...n2) => {
    n2.length === 0 ? m2(i) : m2((y2) => n2.filter((P2) => has(i, P2)).reduce((P2, C2) => set(P2, C2, get(i, C2)), { ...y2 }));
  }, [m2, i]), j2 = (0, import_react9.useCallback)((n2, y2) => {
    e((P2) => {
      let C2 = { ...P2, ...typeof n2 == "string" ? { [n2]: y2 } : n2 };
      return r4(Object.keys(C2).length > 0), C2;
    });
  }, [e, r4]), L2 = (0, import_react9.useCallback)((...n2) => {
    e((y2) => {
      let P2 = Object.keys(y2).reduce((C2, o) => ({ ...C2, ...n2.length > 0 && !n2.includes(o) ? { [o]: y2[o] } : {} }), {});
      return r4(Object.keys(P2).length > 0), P2;
    });
  }, [e, r4]), k2 = (n2) => (y2, P2) => {
    b2(n2, y2, P2);
  }, ee2 = (0, import_react9.useCallback)(k2("get"), [b2]), A2 = (0, import_react9.useCallback)(k2("post"), [b2]), $2 = (0, import_react9.useCallback)(k2("put"), [b2]), W2 = (0, import_react9.useCallback)(k2("patch"), [b2]), H2 = (0, import_react9.useCallback)(k2("delete"), [b2]), R = (0, import_react9.useCallback)(() => {
    u.current && u.current.cancel();
  }, []), te2 = (0, import_react9.useCallback)((n2) => {
    v2.current = n2;
  }, []);
  return { data: c, setData: G2, isDirty: !isEqual(c, i), errors: l, hasErrors: d, processing: h, progress: I, wasSuccessful: Y2, recentlySuccessful: z2, transform: te2, setDefaults: X2, reset: Z2, setError: j2, clearErrors: L2, submit: b2, get: ee2, post: A2, put: $2, patch: W2, delete: H2, cancel: R };
}
function Te2(s, t = {}, a2 = { keepAlive: false, autoStart: true }) {
  let p = (0, import_react11.useRef)(Wr.poll(s, t, { ...a2, autoStart: false }));
  return (0, import_react11.useEffect)(() => ((a2.autoStart ?? true) && p.current.start(), () => p.current.stop()), []), { stop: p.current.stop, start: p.current.start };
}
function be2(s = {}) {
  let t = typeof window > "u" ? null : Wr.getCached(window.location.pathname, s), a2 = typeof window > "u" ? null : Wr.getPrefetching(window.location.pathname, s), [p, i] = (0, import_react12.useState)((t == null ? void 0 : t.staleTimestamp) || null), [g2, u] = (0, import_react12.useState)(a2 !== null), [f2, c] = (0, import_react12.useState)(t !== null);
  return (0, import_react12.useEffect)(() => {
    let m2 = Wr.on("prefetching", (e) => {
      e.detail.visit.url.pathname === window.location.pathname && u(true);
    }), l = Wr.on("prefetched", (e) => {
      e.detail.visit.url.pathname === window.location.pathname && (u(false), c(true), i(e.detail.fetchedAt));
    });
    return () => {
      l(), m2();
    };
  }, []), { lastUpdatedAt: p, isPrefetching: g2, isPrefetched: f2, flush: () => Wr.flush(window.location.pathname, s) };
}
var Ce2 = ({ children: s, data: t, params: a2, buffer: p, as: i, always: g2, fallback: u }) => {
  g2 = g2 ?? false, i = i ?? "div", u = u ?? null;
  let [f2, c] = (0, import_react13.useState)(false), m2 = (0, import_react13.useRef)(false), l = (0, import_react13.useRef)(false), e = (0, import_react13.useRef)(null), d = (0, import_react13.useCallback)(() => {
    if (t) return { only: Array.isArray(t) ? t : [t] };
    if (!a2) throw new Error("You must provide either a `data` or `params` prop.");
    return a2;
  }, [a2, t]);
  return (0, import_react13.useEffect)(() => {
    if (!e.current) return;
    let r4 = new IntersectionObserver((h) => {
      if (!h[0].isIntersecting || (!g2 && m2.current && r4.disconnect(), l.current)) return;
      m2.current = true, l.current = true;
      let F2 = d();
      Wr.reload({ ...F2, onStart: (I) => {
        var _a;
        l.current = true, (_a = F2.onStart) == null ? void 0 : _a.call(F2, I);
      }, onFinish: (I) => {
        var _a;
        c(true), l.current = false, (_a = F2.onFinish) == null ? void 0 : _a.call(F2, I), g2 || r4.disconnect();
      } });
    }, { rootMargin: `${p || 0}px` });
    return r4.observe(e.current), () => {
      r4.disconnect();
    };
  }, [e, d, p]), g2 || !f2 ? (0, import_react13.createElement)(i, { props: null, ref: e }, f2 ? s : u) : f2 ? s : null;
};
Ce2.displayName = "InertiaWhenVisible";
var lt3 = Ce2;
var me2 = Wr;
export {
  Be2 as Deferred,
  $e2 as Head,
  Ye2 as Link,
  lt3 as WhenVisible,
  ce2 as createInertiaApp,
  me2 as router,
  ve2 as useForm,
  q2 as usePage,
  Te2 as usePoll,
  be2 as usePrefetch,
  V2 as useRemember
};
/*! Bundled license information:

@inertiajs/core/dist/index.esm.js:
  (* NProgress, (c) 2013, 2014 Rico Sta. Cruz - http://ricostacruz.com/nprogress
   * @license MIT *)
*/
//# sourceMappingURL=@inertiajs_react.js.map
